"""
Redis Client for the Multi-Agent Development System.

This module provides Redis integration for message queuing, caching,
and inter-agent communication.
"""

import asyncio
import json
import pickle
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta

import aioredis
from aioredis import Redis

from ..models import Message, MessageType
from ..utils import (
    get_logger, handle_error, DatabaseException, ErrorCode,
    get_config, log_performance
)


class RedisClient:
    """Redis client for message queuing and caching."""
    
    def __init__(
        self,
        redis_url: Optional[str] = None,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        **kwargs
    ):
        """Initialize Redis client."""
        self.logger = get_logger("redis_client")
        
        # Connection parameters
        self.redis_url = redis_url or get_config("redis_url")
        self.host = host or get_config("redis_host", "localhost")
        self.port = port or get_config("redis_port", 6379)
        self.db = db or get_config("redis_db", 0)
        self.password = password or get_config("redis_password")
        
        # Redis connection
        self.redis: Optional[Redis] = None
        self.connected = False
        
        # Message queues and channels
        self.message_queue_key = "agent_messages"
        self.broadcast_channel = "agent_broadcast"
        self.agent_status_key = "agent_status"
        self.task_queue_key = "task_queue"
        
        # Subscribers
        self.subscribers: Dict[str, Callable] = {}
        self.pubsub = None
        
        self.logger.info("Redis client initialized")
    
    async def connect(self) -> bool:
        """Connect to Redis."""
        try:
            if self.redis_url:
                self.redis = await aioredis.from_url(
                    self.redis_url,
                    encoding="utf-8",
                    decode_responses=True
                )
            else:
                self.redis = await aioredis.Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    encoding="utf-8",
                    decode_responses=True
                )
            
            # Test connection
            await self.redis.ping()
            self.connected = True
            
            self.logger.info("Connected to Redis successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.connected = False
            raise DatabaseException(
                f"Redis connection failed: {str(e)}",
                database_type="redis",
                operation="connect",
                error_code=ErrorCode.REDIS_CONNECTION_ERROR
            )
    
    async def disconnect(self) -> None:
        """Disconnect from Redis."""
        try:
            if self.pubsub:
                await self.pubsub.close()
                self.pubsub = None
            
            if self.redis:
                await self.redis.close()
                self.redis = None
            
            self.connected = False
            self.logger.info("Disconnected from Redis")
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from Redis: {e}")
    
    async def ensure_connected(self) -> None:
        """Ensure Redis connection is active."""
        if not self.connected or not self.redis:
            await self.connect()
    
    # Message Queue Operations
    async def send_message(self, message: Message, queue: Optional[str] = None) -> bool:
        """Send message to queue."""
        try:
            await self.ensure_connected()
            
            queue_key = queue or self.message_queue_key
            message_data = json.dumps(message.to_dict())
            
            with log_performance("redis_send_message", self.logger):
                await self.redis.lpush(queue_key, message_data)
            
            self.logger.debug(f"Message sent to queue {queue_key}: {message.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            handle_error(e, {"operation": "send_message", "queue": queue})
            return False
    
    async def receive_message(self, queue: Optional[str] = None, timeout: int = 1) -> Optional[Message]:
        """Receive message from queue."""
        try:
            await self.ensure_connected()
            
            queue_key = queue or self.message_queue_key
            
            with log_performance("redis_receive_message", self.logger):
                result = await self.redis.brpop(queue_key, timeout=timeout)
            
            if result:
                _, message_data = result
                message_dict = json.loads(message_data)
                message = Message(**message_dict)
                
                self.logger.debug(f"Message received from queue {queue_key}: {message.id}")
                return message
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to receive message: {e}")
            handle_error(e, {"operation": "receive_message", "queue": queue})
            return None
    
    async def get_queue_length(self, queue: Optional[str] = None) -> int:
        """Get queue length."""
        try:
            await self.ensure_connected()
            
            queue_key = queue or self.message_queue_key
            length = await self.redis.llen(queue_key)
            return length
            
        except Exception as e:
            self.logger.error(f"Failed to get queue length: {e}")
            return 0
    
    # Pub/Sub Operations
    async def publish_message(self, channel: str, message: Message) -> bool:
        """Publish message to channel."""
        try:
            await self.ensure_connected()
            
            message_data = json.dumps(message.to_dict())
            
            with log_performance("redis_publish_message", self.logger):
                await self.redis.publish(channel, message_data)
            
            self.logger.debug(f"Message published to channel {channel}: {message.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to publish message: {e}")
            handle_error(e, {"operation": "publish_message", "channel": channel})
            return False
    
    async def subscribe_to_channel(self, channel: str, callback: Callable[[Message], None]) -> bool:
        """Subscribe to channel."""
        try:
            await self.ensure_connected()
            
            if not self.pubsub:
                self.pubsub = self.redis.pubsub()
            
            await self.pubsub.subscribe(channel)
            self.subscribers[channel] = callback
            
            self.logger.info(f"Subscribed to channel: {channel}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to subscribe to channel {channel}: {e}")
            return False
    
    async def unsubscribe_from_channel(self, channel: str) -> bool:
        """Unsubscribe from channel."""
        try:
            if self.pubsub:
                await self.pubsub.unsubscribe(channel)
                self.subscribers.pop(channel, None)
                
                self.logger.info(f"Unsubscribed from channel: {channel}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from channel {channel}: {e}")
            return False
    
    async def listen_for_messages(self) -> None:
        """Listen for published messages."""
        try:
            if not self.pubsub:
                return
            
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    channel = message["channel"]
                    data = message["data"]
                    
                    try:
                        message_dict = json.loads(data)
                        msg = Message(**message_dict)
                        
                        # Call subscriber callback
                        if channel in self.subscribers:
                            callback = self.subscribers[channel]
                            await callback(msg) if asyncio.iscoroutinefunction(callback) else callback(msg)
                            
                    except Exception as e:
                        self.logger.error(f"Error processing message from channel {channel}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error listening for messages: {e}")
    
    # Caching Operations
    async def set_cache(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set cache value."""
        try:
            await self.ensure_connected()
            
            # Serialize value
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            elif isinstance(value, str):
                serialized_value = value
            else:
                serialized_value = pickle.dumps(value)
            
            with log_performance("redis_set_cache", self.logger):
                if ttl:
                    await self.redis.setex(key, ttl, serialized_value)
                else:
                    await self.redis.set(key, serialized_value)
            
            self.logger.debug(f"Cache set: {key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set cache: {e}")
            return False
    
    async def get_cache(self, key: str, default: Any = None) -> Any:
        """Get cache value."""
        try:
            await self.ensure_connected()
            
            with log_performance("redis_get_cache", self.logger):
                value = await self.redis.get(key)
            
            if value is None:
                return default
            
            # Try to deserialize
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                try:
                    return pickle.loads(value.encode() if isinstance(value, str) else value)
                except:
                    return value
                    
        except Exception as e:
            self.logger.error(f"Failed to get cache: {e}")
            return default
    
    async def delete_cache(self, key: str) -> bool:
        """Delete cache value."""
        try:
            await self.ensure_connected()
            
            result = await self.redis.delete(key)
            self.logger.debug(f"Cache deleted: {key}")
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to delete cache: {e}")
            return False
    
    async def cache_exists(self, key: str) -> bool:
        """Check if cache key exists."""
        try:
            await self.ensure_connected()
            
            result = await self.redis.exists(key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"Failed to check cache existence: {e}")
            return False
    
    # Agent Status Operations
    async def set_agent_status(self, agent_id: str, status_data: Dict[str, Any]) -> bool:
        """Set agent status."""
        try:
            status_key = f"{self.agent_status_key}:{agent_id}"
            return await self.set_cache(status_key, status_data, ttl=300)  # 5 minutes TTL
            
        except Exception as e:
            self.logger.error(f"Failed to set agent status: {e}")
            return False
    
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get agent status."""
        try:
            status_key = f"{self.agent_status_key}:{agent_id}"
            return await self.get_cache(status_key)
            
        except Exception as e:
            self.logger.error(f"Failed to get agent status: {e}")
            return None
    
    async def get_all_agent_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get all agent statuses."""
        try:
            await self.ensure_connected()
            
            pattern = f"{self.agent_status_key}:*"
            keys = await self.redis.keys(pattern)
            
            statuses = {}
            for key in keys:
                agent_id = key.split(":")[-1]
                status = await self.get_cache(key)
                if status:
                    statuses[agent_id] = status
            
            return statuses
            
        except Exception as e:
            self.logger.error(f"Failed to get all agent statuses: {e}")
            return {}
    
    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            await self.ensure_connected()
            
            # Test basic operations
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.utcnow().isoformat()}
            
            # Test set/get
            await self.set_cache(test_key, test_value, ttl=10)
            retrieved = await self.get_cache(test_key)
            
            # Test queue operations
            queue_length = await self.get_queue_length()
            
            # Cleanup
            await self.delete_cache(test_key)
            
            return {
                "status": "healthy",
                "connected": self.connected,
                "set_get_test": retrieved == test_value,
                "queue_length": queue_length,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "connected": False,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
