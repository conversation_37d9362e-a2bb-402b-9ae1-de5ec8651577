"""
Communication module for the Multi-Agent Development System.

This module provides inter-agent communication capabilities including
message routing, Redis integration, and real-time messaging.
"""

from .redis_client import RedisClient
from .hub import CommunicationHub

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"

# Export all public classes
__all__ = [
    "RedisClient",
    "CommunicationHub",
]