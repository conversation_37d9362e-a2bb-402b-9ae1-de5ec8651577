"""
Unit tests for data models in the Multi-Agent Development System.

This module tests the core data models including Agent, Task, Message,
and their associated functionality.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from shared.models import (
    Agent, AgentRole, AgentStatus, AgentCapabilities, AgentConfiguration,
    Task, TaskType, TaskStatus, TaskPriority, TaskResult,
    Message, MessageType, MessageMetadata,
    create_agent, create_task, create_task_message, create_response_message,
    LLMProvider, ToolType
)


class TestAgent:
    """Test Agent model."""
    
    def test_agent_creation(self, sample_agent_config, sample_agent_capabilities):
        """Test agent creation."""
        agent = create_agent(
            name="Test Agent",
            role=AgentRole.DEVELOPER,
            description="Test agent",
            llm_provider=LLMProvider.OPENAI,
            model_name="gpt-4-turbo",
            capabilities=sample_agent_capabilities,
            configuration=sample_agent_config
        )
        
        assert agent.name == "Test Agent"
        assert agent.role == AgentRole.DEVELOPER
        assert agent.description == "Test agent"
        assert agent.state.status == AgentStatus.OFFLINE
        assert agent.is_online() is False
        assert agent.is_available() is False
    
    def test_agent_state_transitions(self, sample_agent):
        """Test agent state transitions."""
        # Start agent
        sample_agent.start()
        assert sample_agent.state.status == AgentStatus.INITIALIZING
        
        # Set to idle
        sample_agent.set_idle()
        assert sample_agent.state.status == AgentStatus.IDLE
        assert sample_agent.is_idle() is True
        assert sample_agent.is_available() is True
        
        # Set to working
        task_id = str(uuid4())
        sample_agent.set_working(task_id)
        assert sample_agent.state.status == AgentStatus.WORKING
        assert sample_agent.is_working() is True
        assert sample_agent.state.current_task_id == task_id
        
        # Complete task
        sample_agent.complete_task(task_id, True)
        assert sample_agent.state.status == AgentStatus.IDLE
        assert sample_agent.state.current_task_id is None
        assert sample_agent.metrics.tasks_completed == 1
    
    def test_agent_error_handling(self, sample_agent):
        """Test agent error handling."""
        error_message = "Test error"
        sample_agent.set_error(error_message)
        
        assert sample_agent.state.status == AgentStatus.ERROR
        assert sample_agent.state.last_error == error_message
        assert sample_agent.state.error_count == 1
        
        # Clear error
        sample_agent.clear_error()
        assert sample_agent.state.status == AgentStatus.IDLE
        assert sample_agent.state.error_count == 0
    
    def test_agent_capabilities(self, sample_agent):
        """Test agent capabilities."""
        assert sample_agent.has_skill("python") is True
        assert sample_agent.has_skill("nonexistent") is False
        
        assert sample_agent.has_tool(ToolType.CODE_GENERATOR) is True
        assert sample_agent.has_tool(ToolType.DEBUGGER) is False
        
        assert sample_agent.supports_language("python") is True
        assert sample_agent.supports_language("rust") is False
    
    def test_agent_metrics(self, sample_agent):
        """Test agent metrics."""
        # Initial metrics
        assert sample_agent.metrics.tasks_completed == 0
        assert sample_agent.metrics.tasks_failed == 0
        assert sample_agent.metrics.success_rate == 0.0
        
        # Complete successful task
        sample_agent.complete_task("task1", True)
        assert sample_agent.metrics.tasks_completed == 1
        assert sample_agent.metrics.success_rate == 1.0
        
        # Complete failed task
        sample_agent.complete_task("task2", False)
        assert sample_agent.metrics.tasks_failed == 1
        assert sample_agent.metrics.success_rate == 0.5


class TestTask:
    """Test Task model."""
    
    def test_task_creation(self):
        """Test task creation."""
        task = create_task(
            title="Test Task",
            description="Test description",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_agent",
            created_by_role=AgentRole.PROJECT_MANAGER,
            priority=TaskPriority.HIGH
        )
        
        assert task.title == "Test Task"
        assert task.description == "Test description"
        assert task.task_type == TaskType.DEVELOPMENT
        assert task.status == TaskStatus.PENDING
        assert task.priority == TaskPriority.HIGH
        assert task.is_pending() is True
    
    def test_task_assignment(self, sample_task):
        """Test task assignment."""
        agent_id = "test_agent"
        agent_role = AgentRole.DEVELOPER
        
        sample_task.assign_to(agent_id, agent_role)
        
        assert sample_task.assigned_to == agent_id
        assert sample_task.assigned_role == agent_role
        assert sample_task.status == TaskStatus.ASSIGNED
        assert sample_task.is_assigned() is True
    
    def test_task_execution_flow(self, sample_task):
        """Test task execution flow."""
        # Assign task
        sample_task.assign_to("test_agent", AgentRole.DEVELOPER)
        assert sample_task.can_start() is True
        
        # Start task
        sample_task.start()
        assert sample_task.status == TaskStatus.IN_PROGRESS
        assert sample_task.is_in_progress() is True
        assert sample_task.started_at is not None
        
        # Complete task
        result = TaskResult(
            success=True,
            output={"result": "completed"},
            duration_seconds=5.0
        )
        sample_task.complete(result)
        
        assert sample_task.status == TaskStatus.COMPLETED
        assert sample_task.is_completed() is True
        assert sample_task.completed_at is not None
        assert sample_task.result == result
        assert sample_task.is_terminal() is True
    
    def test_task_failure(self, sample_task):
        """Test task failure handling."""
        sample_task.assign_to("test_agent", AgentRole.DEVELOPER)
        sample_task.start()
        
        error_message = "Task failed"
        sample_task.fail(error_message)
        
        assert sample_task.status == TaskStatus.FAILED
        assert sample_task.is_failed() is True
        assert sample_task.result.success is False
        assert sample_task.result.error == error_message
    
    def test_task_retry(self, sample_task):
        """Test task retry functionality."""
        sample_task.assign_to("test_agent", AgentRole.DEVELOPER)
        sample_task.start()
        sample_task.fail("Initial failure")
        
        assert sample_task.can_retry() is True
        
        sample_task.retry()
        assert sample_task.status == TaskStatus.ASSIGNED
        assert sample_task.retry_count == 1
        assert sample_task.started_at is None
        assert sample_task.completed_at is None
        assert sample_task.result is None
    
    def test_task_dependencies(self, sample_task):
        """Test task dependencies."""
        dep_task_id = "dependency_task"
        
        sample_task.add_dependency(dep_task_id)
        assert dep_task_id in sample_task.dependencies
        
        sample_task.remove_dependency(dep_task_id)
        assert dep_task_id not in sample_task.dependencies
    
    def test_task_deadline(self):
        """Test task deadline functionality."""
        future_deadline = datetime.utcnow() + timedelta(hours=1)
        past_deadline = datetime.utcnow() - timedelta(hours=1)
        
        # Future deadline
        task1 = create_task(
            title="Future Task",
            description="Task with future deadline",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_agent",
            created_by_role=AgentRole.PROJECT_MANAGER,
            deadline=future_deadline
        )
        assert task1.is_overdue() is False
        
        # Past deadline
        task2 = create_task(
            title="Overdue Task",
            description="Task with past deadline",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_agent",
            created_by_role=AgentRole.PROJECT_MANAGER,
            deadline=past_deadline
        )
        assert task2.is_overdue() is True


class TestMessage:
    """Test Message model."""
    
    def test_message_creation(self):
        """Test message creation."""
        message = Message(
            sender_id="sender",
            sender_role=AgentRole.PROJECT_MANAGER,
            receiver_id="receiver",
            receiver_role=AgentRole.DEVELOPER,
            message_type=MessageType.TASK,
            content="Test message"
        )
        
        assert message.sender_id == "sender"
        assert message.receiver_id == "receiver"
        assert message.message_type == MessageType.TASK
        assert message.content == "Test message"
        assert message.is_direct() is True
        assert message.is_broadcast() is False
    
    def test_broadcast_message(self):
        """Test broadcast message."""
        message = Message(
            sender_id="sender",
            sender_role=AgentRole.PROJECT_MANAGER,
            receiver_id=None,
            message_type=MessageType.BROADCAST,
            content="Broadcast message"
        )
        
        assert message.is_broadcast() is True
        assert message.is_direct() is False
    
    def test_task_message_creation(self):
        """Test task message creation."""
        message = create_task_message(
            sender_id="pm_agent",
            sender_role=AgentRole.PROJECT_MANAGER,
            task_id="task_123",
            action="execute",
            receiver_id="dev_agent",
            receiver_role=AgentRole.DEVELOPER,
            parameters={"priority": "high"}
        )
        
        assert message.message_type == MessageType.TASK
        assert message.task_id == "task_123"
        assert message.action == "execute"
        assert message.parameters["priority"] == "high"
    
    def test_response_message_creation(self, sample_message):
        """Test response message creation."""
        response = create_response_message(
            sender_id="dev_agent",
            sender_role=AgentRole.DEVELOPER,
            original_message=sample_message,
            success=True,
            result={"status": "completed"}
        )
        
        assert response.message_type == MessageType.RESPONSE
        assert response.original_message_id == sample_message.id
        assert response.success is True
        assert response.result["status"] == "completed"
        assert response.receiver_id == sample_message.sender_id
    
    def test_message_metadata(self):
        """Test message metadata."""
        metadata = MessageMetadata(
            priority=3,
            requires_response=True,
            response_timeout=30,
            tags=["urgent", "development"]
        )
        
        message = Message(
            sender_id="sender",
            sender_role=AgentRole.PROJECT_MANAGER,
            message_type=MessageType.TASK,
            content="Test message",
            metadata=metadata
        )
        
        assert message.requires_response() is True
        assert message.has_tag("urgent") is True
        assert message.has_tag("testing") is False
        
        message.add_tag("testing")
        assert message.has_tag("testing") is True
    
    def test_message_expiration(self):
        """Test message expiration."""
        metadata = MessageMetadata(response_timeout=1)  # 1 second timeout
        
        message = Message(
            sender_id="sender",
            sender_role=AgentRole.PROJECT_MANAGER,
            message_type=MessageType.TASK,
            content="Test message",
            metadata=metadata
        )
        
        # Should not be expired immediately
        assert message.is_expired() is False
        
        # Simulate time passing (in real test, would need to wait or mock time)
        import time
        time.sleep(1.1)
        assert message.is_expired() is True


class TestEnums:
    """Test enum functionality."""
    
    def test_agent_status_transitions(self):
        """Test agent status transition validation."""
        from shared.models.enums import can_transition_agent_status
        
        # Valid transitions
        assert can_transition_agent_status(AgentStatus.OFFLINE, AgentStatus.INITIALIZING) is True
        assert can_transition_agent_status(AgentStatus.IDLE, AgentStatus.WORKING) is True
        assert can_transition_agent_status(AgentStatus.WORKING, AgentStatus.IDLE) is True
        
        # Invalid transitions
        assert can_transition_agent_status(AgentStatus.OFFLINE, AgentStatus.WORKING) is False
        assert can_transition_agent_status(AgentStatus.WORKING, AgentStatus.OFFLINE) is False
    
    def test_task_status_transitions(self):
        """Test task status transition validation."""
        from shared.models.enums import can_transition_task_status
        
        # Valid transitions
        assert can_transition_task_status(TaskStatus.PENDING, TaskStatus.ASSIGNED) is True
        assert can_transition_task_status(TaskStatus.ASSIGNED, TaskStatus.IN_PROGRESS) is True
        assert can_transition_task_status(TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED) is True
        
        # Invalid transitions
        assert can_transition_task_status(TaskStatus.PENDING, TaskStatus.COMPLETED) is False
        assert can_transition_task_status(TaskStatus.COMPLETED, TaskStatus.IN_PROGRESS) is False
    
    def test_priority_comparison(self):
        """Test task priority comparison."""
        from shared.models.enums import compare_priorities, get_priority_value
        
        assert get_priority_value(TaskPriority.CRITICAL) > get_priority_value(TaskPriority.HIGH)
        assert get_priority_value(TaskPriority.HIGH) > get_priority_value(TaskPriority.MEDIUM)
        
        assert compare_priorities(TaskPriority.HIGH, TaskPriority.LOW) == 1
        assert compare_priorities(TaskPriority.LOW, TaskPriority.HIGH) == -1
        assert compare_priorities(TaskPriority.MEDIUM, TaskPriority.MEDIUM) == 0
