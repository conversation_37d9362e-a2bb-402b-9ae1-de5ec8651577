"""
Tool Manager for the Multi-Agent Development System.

This module provides a centralized tool management system that allows agents
to discover, load, and execute various tools for different tasks.
"""

import asyncio
import importlib
import inspect
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union, Callable
from dataclasses import dataclass

from ..models.enums import ToolType, AgentRole
from ..utils import (
    get_logger, handle_error, ToolException, ErrorCode,
    log_performance
)


@dataclass
class ToolResult:
    """Result from tool execution."""
    success: bool
    output: Any
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ToolInfo:
    """Information about a tool."""
    name: str
    tool_type: ToolType
    description: str
    parameters: Dict[str, Any]
    required_permissions: List[str]
    supported_agents: List[AgentRole]
    version: str = "1.0.0"
    author: str = "inkbytefo"


class BaseTool(ABC):
    """Base class for all tools."""
    
    def __init__(self, name: str, tool_type: ToolType, description: str):
        self.name = name
        self.tool_type = tool_type
        self.description = description
        self.logger = get_logger(f"tool.{name}")
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters."""
        pass
    
    @abstractmethod
    def get_info(self) -> ToolInfo:
        """Get tool information."""
        pass
    
    @abstractmethod
    def validate_parameters(self, **kwargs) -> bool:
        """Validate tool parameters."""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool schema for LLM function calling."""
        info = self.get_info()
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": info.parameters
            }
        }


class CodeGeneratorTool(BaseTool):
    """Tool for generating code."""
    
    def __init__(self):
        super().__init__(
            name="code_generator",
            tool_type=ToolType.CODE_GENERATOR,
            description="Generate code based on specifications"
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute code generation."""
        try:
            specification = kwargs.get("specification", "")
            language = kwargs.get("language", "python")
            style = kwargs.get("style", "clean")
            
            if not specification:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Specification is required"
                )
            
            # This would integrate with LLM for actual code generation
            # For now, return a placeholder
            generated_code = f"""
# Generated {language} code for: {specification}
# Style: {style}

def placeholder_function():
    '''
    This is a placeholder for generated code.
    In a real implementation, this would be generated by an LLM.
    '''
    pass
"""
            
            return ToolResult(
                success=True,
                output=generated_code,
                metadata={
                    "language": language,
                    "style": style,
                    "lines": len(generated_code.split('\n'))
                }
            )
            
        except Exception as e:
            self.logger.error(f"Code generation failed: {e}")
            return ToolResult(
                success=False,
                output=None,
                error=str(e)
            )
    
    def get_info(self) -> ToolInfo:
        """Get tool information."""
        return ToolInfo(
            name=self.name,
            tool_type=self.tool_type,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "specification": {
                        "type": "string",
                        "description": "Code specification or requirements"
                    },
                    "language": {
                        "type": "string",
                        "description": "Programming language",
                        "default": "python"
                    },
                    "style": {
                        "type": "string",
                        "description": "Code style preference",
                        "default": "clean"
                    }
                },
                "required": ["specification"]
            },
            required_permissions=["code_generation"],
            supported_agents=[AgentRole.DEVELOPER]
        )
    
    def validate_parameters(self, **kwargs) -> bool:
        """Validate parameters."""
        return "specification" in kwargs and isinstance(kwargs["specification"], str)


class FileManagerTool(BaseTool):
    """Tool for file operations."""
    
    def __init__(self):
        super().__init__(
            name="file_manager",
            tool_type=ToolType.FILE_MANAGER,
            description="Manage files and directories"
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute file operation."""
        try:
            operation = kwargs.get("operation", "")
            path = kwargs.get("path", "")
            content = kwargs.get("content", "")
            
            if operation == "read":
                return await self._read_file(path)
            elif operation == "write":
                return await self._write_file(path, content)
            elif operation == "list":
                return await self._list_directory(path)
            elif operation == "create_dir":
                return await self._create_directory(path)
            else:
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Unknown operation: {operation}"
                )
                
        except Exception as e:
            self.logger.error(f"File operation failed: {e}")
            return ToolResult(
                success=False,
                output=None,
                error=str(e)
            )
    
    async def _read_file(self, path: str) -> ToolResult:
        """Read file content."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return ToolResult(
                success=True,
                output=content,
                metadata={"path": path, "size": len(content)}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"Failed to read file: {e}"
            )
    
    async def _write_file(self, path: str, content: str) -> ToolResult:
        """Write content to file."""
        try:
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return ToolResult(
                success=True,
                output=f"File written: {path}",
                metadata={"path": path, "size": len(content)}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"Failed to write file: {e}"
            )
    
    async def _list_directory(self, path: str) -> ToolResult:
        """List directory contents."""
        try:
            import os
            items = os.listdir(path)
            
            return ToolResult(
                success=True,
                output=items,
                metadata={"path": path, "count": len(items)}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"Failed to list directory: {e}"
            )
    
    async def _create_directory(self, path: str) -> ToolResult:
        """Create directory."""
        try:
            import os
            os.makedirs(path, exist_ok=True)
            
            return ToolResult(
                success=True,
                output=f"Directory created: {path}",
                metadata={"path": path}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"Failed to create directory: {e}"
            )
    
    def get_info(self) -> ToolInfo:
        """Get tool information."""
        return ToolInfo(
            name=self.name,
            tool_type=self.tool_type,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "description": "File operation to perform",
                        "enum": ["read", "write", "list", "create_dir"]
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write (for write operation)"
                    }
                },
                "required": ["operation", "path"]
            },
            required_permissions=["file_access"],
            supported_agents=[AgentRole.DEVELOPER, AgentRole.DOCUMENTER]
        )
    
    def validate_parameters(self, **kwargs) -> bool:
        """Validate parameters."""
        operation = kwargs.get("operation")
        path = kwargs.get("path")
        
        if not operation or not path:
            return False
        
        if operation == "write" and "content" not in kwargs:
            return False
        
        return True


class ToolManager:
    """Manages tools for agents."""
    
    def __init__(self, agent_id: str, available_tools: Optional[List[ToolType]] = None):
        self.agent_id = agent_id
        self.available_tools = available_tools or []
        self.logger = get_logger(f"tool_manager.{agent_id}")
        
        # Registry of loaded tools
        self.tools: Dict[str, BaseTool] = {}
        self.tool_schemas: Dict[str, Dict[str, Any]] = {}
        
        # Load default tools
        self._load_default_tools()
    
    def _load_default_tools(self) -> None:
        """Load default tools."""
        default_tools = [
            CodeGeneratorTool(),
            FileManagerTool(),
        ]
        
        for tool in default_tools:
            if tool.tool_type in self.available_tools:
                self.register_tool(tool)
    
    def register_tool(self, tool: BaseTool) -> bool:
        """Register a tool."""
        try:
            self.tools[tool.name] = tool
            self.tool_schemas[tool.name] = tool.get_schema()
            self.logger.info(f"Registered tool: {tool.name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to register tool {tool.name}: {e}")
            return False
    
    def unregister_tool(self, tool_name: str) -> bool:
        """Unregister a tool."""
        try:
            if tool_name in self.tools:
                del self.tools[tool_name]
                del self.tool_schemas[tool_name]
                self.logger.info(f"Unregistered tool: {tool_name}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to unregister tool {tool_name}: {e}")
            return False
    
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a tool."""
        try:
            if tool_name not in self.tools:
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Tool not found: {tool_name}"
                )
            
            tool = self.tools[tool_name]
            
            # Validate parameters
            if not tool.validate_parameters(**kwargs):
                return ToolResult(
                    success=False,
                    output=None,
                    error="Invalid parameters"
                )
            
            # Execute tool with performance logging
            with log_performance(f"tool_execution_{tool_name}", self.logger):
                result = await tool.execute(**kwargs)
            
            self.logger.debug(f"Tool {tool_name} executed: {result.success}")
            return result
            
        except Exception as e:
            self.logger.error(f"Tool execution failed: {e}")
            return ToolResult(
                success=False,
                output=None,
                error=str(e)
            )
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return list(self.tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[ToolInfo]:
        """Get information about a tool."""
        if tool_name in self.tools:
            return self.tools[tool_name].get_info()
        return None
    
    def get_tool_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Get schemas for all tools (for LLM function calling)."""
        return self.tool_schemas.copy()
    
    def get_tools_by_type(self, tool_type: ToolType) -> List[str]:
        """Get tools by type."""
        return [
            name for name, tool in self.tools.items()
            if tool.tool_type == tool_type
        ]
