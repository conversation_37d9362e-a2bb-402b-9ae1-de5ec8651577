"""
LLM Client for the Multi-Agent Development System.

This module provides a unified interface for interacting with different LLM providers
including OpenAI, Anthropic, and Ollama.
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass

from ..models.enums import LLMProvider
from ..utils import (
    get_logger, handle_error, LLMException, ErrorCode,
    get_config, log_performance
)


@dataclass
class LLMResponse:
    """Response from LLM provider."""
    content: str
    model: str
    provider: str
    usage: Dict[str, Any]
    metadata: Dict[str, Any]
    success: bool = True
    error: Optional[str] = None


@dataclass
class LLMRequest:
    """Request to LLM provider."""
    prompt: str
    model: Optional[str] = None
    temperature: float = 0.3
    max_tokens: int = 2000
    system_prompt: Optional[str] = None
    context: Optional[List[Dict[str, str]]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.context is None:
            self.context = []


class BaseLLMProvider(ABC):
    """Base class for LLM providers."""
    
    def __init__(self, provider_name: str, **config):
        self.provider_name = provider_name
        self.config = config
        self.logger = get_logger(f"llm.{provider_name}")
    
    @abstractmethod
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response from LLM."""
        pass
    
    @abstractmethod
    async def validate_config(self) -> bool:
        """Validate provider configuration."""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models."""
        pass


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, **config):
        super().__init__("openai", **config)
        self.api_key = config.get("api_key")
        self.org_id = config.get("org_id")
        self.base_url = config.get("base_url", "https://api.openai.com/v1")
        self.client = None
    
    async def _initialize_client(self):
        """Initialize OpenAI client."""
        if self.client is None:
            try:
                import openai
                self.client = openai.AsyncOpenAI(
                    api_key=self.api_key,
                    organization=self.org_id,
                    base_url=self.base_url
                )
            except ImportError:
                raise LLMException(
                    "OpenAI library not installed. Install with: pip install openai",
                    provider="openai",
                    error_code=ErrorCode.LLM_API_ERROR
                )
    
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response using OpenAI."""
        try:
            await self._initialize_client()
            
            # Prepare messages
            messages = []
            
            if request.system_prompt:
                messages.append({"role": "system", "content": request.system_prompt})
            
            # Add context messages
            if request.context:
                messages.extend(request.context)
            
            # Add user prompt
            messages.append({"role": "user", "content": request.prompt})
            
            # Prepare request parameters
            params = {
                "model": request.model or self.config.get("default_model", "gpt-4-turbo"),
                "messages": messages,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
            }
            
            # Add tools if provided
            if request.tools:
                params["tools"] = request.tools
                params["tool_choice"] = "auto"
            
            # Make API call with performance logging
            with log_performance(f"openai_generate_{params['model']}", self.logger):
                response = await self.client.chat.completions.create(**params)
            
            # Extract response content
            content = response.choices[0].message.content or ""
            
            # Handle tool calls if present
            tool_calls = response.choices[0].message.tool_calls
            if tool_calls:
                request.metadata["tool_calls"] = [
                    {
                        "id": call.id,
                        "function": call.function.name,
                        "arguments": call.function.arguments
                    }
                    for call in tool_calls
                ]
            
            return LLMResponse(
                content=content,
                model=response.model,
                provider="openai",
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens,
                },
                metadata=request.metadata
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            return LLMResponse(
                content="",
                model=request.model or "unknown",
                provider="openai",
                usage={},
                metadata=request.metadata,
                success=False,
                error=str(e)
            )
    
    async def validate_config(self) -> bool:
        """Validate OpenAI configuration."""
        if not self.api_key:
            return False
        
        try:
            await self._initialize_client()
            # Test with a simple request
            await self.client.models.list()
            return True
        except Exception as e:
            self.logger.error(f"OpenAI config validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get available OpenAI models."""
        return [
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
        ]


class AnthropicProvider(BaseLLMProvider):
    """Anthropic LLM provider."""
    
    def __init__(self, **config):
        super().__init__("anthropic", **config)
        self.api_key = config.get("api_key")
        self.client = None
    
    async def _initialize_client(self):
        """Initialize Anthropic client."""
        if self.client is None:
            try:
                import anthropic
                self.client = anthropic.AsyncAnthropic(api_key=self.api_key)
            except ImportError:
                raise LLMException(
                    "Anthropic library not installed. Install with: pip install anthropic",
                    provider="anthropic",
                    error_code=ErrorCode.LLM_API_ERROR
                )
    
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response using Anthropic."""
        try:
            await self._initialize_client()
            
            # Prepare messages
            messages = []
            
            # Add context messages
            if request.context:
                messages.extend(request.context)
            
            # Add user prompt
            messages.append({"role": "user", "content": request.prompt})
            
            # Prepare request parameters
            params = {
                "model": request.model or self.config.get("default_model", "claude-3-sonnet-20240229"),
                "messages": messages,
                "temperature": request.temperature,
                "max_tokens": request.max_tokens,
            }
            
            # Add system prompt if provided
            if request.system_prompt:
                params["system"] = request.system_prompt
            
            # Make API call with performance logging
            with log_performance(f"anthropic_generate_{params['model']}", self.logger):
                response = await self.client.messages.create(**params)
            
            # Extract response content
            content = ""
            for content_block in response.content:
                if content_block.type == "text":
                    content += content_block.text
            
            return LLMResponse(
                content=content,
                model=response.model,
                provider="anthropic",
                usage={
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens,
                },
                metadata=request.metadata
            )
            
        except Exception as e:
            self.logger.error(f"Anthropic API error: {e}")
            return LLMResponse(
                content="",
                model=request.model or "unknown",
                provider="anthropic",
                usage={},
                metadata=request.metadata,
                success=False,
                error=str(e)
            )
    
    async def validate_config(self) -> bool:
        """Validate Anthropic configuration."""
        if not self.api_key:
            return False
        
        try:
            await self._initialize_client()
            # Test with a simple request
            await self.client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=10,
                messages=[{"role": "user", "content": "Hi"}]
            )
            return True
        except Exception as e:
            self.logger.error(f"Anthropic config validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get available Anthropic models."""
        return [
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            "claude-3-opus-20240229",
        ]


class OllamaProvider(BaseLLMProvider):
    """Ollama LLM provider."""
    
    def __init__(self, **config):
        super().__init__("ollama", **config)
        self.base_url = config.get("base_url", "http://localhost:11434")
        self.client = None
    
    async def _initialize_client(self):
        """Initialize Ollama client."""
        if self.client is None:
            try:
                import httpx
                self.client = httpx.AsyncClient(base_url=self.base_url)
            except ImportError:
                raise LLMException(
                    "httpx library not installed. Install with: pip install httpx",
                    provider="ollama",
                    error_code=ErrorCode.LLM_API_ERROR
                )
    
    async def generate(self, request: LLMRequest) -> LLMResponse:
        """Generate response using Ollama."""
        try:
            await self._initialize_client()
            
            # Prepare prompt with context
            full_prompt = ""
            if request.system_prompt:
                full_prompt += f"System: {request.system_prompt}\n\n"
            
            if request.context:
                for msg in request.context:
                    role = msg.get("role", "user")
                    content = msg.get("content", "")
                    full_prompt += f"{role.title()}: {content}\n"
            
            full_prompt += f"User: {request.prompt}\nAssistant:"
            
            # Prepare request
            payload = {
                "model": request.model or self.config.get("default_model", "llama2"),
                "prompt": full_prompt,
                "options": {
                    "temperature": request.temperature,
                    "num_predict": request.max_tokens,
                }
            }
            
            # Make API call
            with log_performance(f"ollama_generate_{payload['model']}", self.logger):
                response = await self.client.post("/api/generate", json=payload)
                response.raise_for_status()
                
                # Ollama returns streaming response, collect all chunks
                content = ""
                for line in response.text.strip().split('\n'):
                    if line:
                        chunk = json.loads(line)
                        if 'response' in chunk:
                            content += chunk['response']
            
            return LLMResponse(
                content=content.strip(),
                model=payload['model'],
                provider="ollama",
                usage={
                    "total_tokens": len(content.split()),  # Approximate
                },
                metadata=request.metadata
            )
            
        except Exception as e:
            self.logger.error(f"Ollama API error: {e}")
            return LLMResponse(
                content="",
                model=request.model or "unknown",
                provider="ollama",
                usage={},
                metadata=request.metadata,
                success=False,
                error=str(e)
            )
    
    async def validate_config(self) -> bool:
        """Validate Ollama configuration."""
        try:
            await self._initialize_client()
            response = await self.client.get("/api/tags")
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Ollama config validation failed: {e}")
            return False
    
    def get_available_models(self) -> List[str]:
        """Get available Ollama models."""
        return [
            "llama2",
            "codellama",
            "mistral",
            "neural-chat",
        ]


class LLMClient:
    """Unified LLM client that manages multiple providers."""

    def __init__(
        self,
        provider: Union[str, LLMProvider],
        model: str,
        temperature: float = 0.3,
        max_tokens: int = 2000,
        fallback_providers: Optional[List[Union[str, LLMProvider]]] = None
    ):
        """Initialize LLM client."""
        self.logger = get_logger("llm.client")

        # Convert string to enum if needed
        if isinstance(provider, str):
            provider = LLMProvider(provider.lower())

        self.primary_provider = provider
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

        # Setup fallback providers
        self.fallback_providers = []
        if fallback_providers:
            for fp in fallback_providers:
                if isinstance(fp, str):
                    fp = LLMProvider(fp.lower())
                self.fallback_providers.append(fp)

        # Initialize providers
        self.providers: Dict[LLMProvider, BaseLLMProvider] = {}
        self._initialize_providers()

    def _initialize_providers(self) -> None:
        """Initialize all configured providers."""
        all_providers = [self.primary_provider] + self.fallback_providers

        for provider in all_providers:
            try:
                config = self._get_provider_config(provider)

                if provider == LLMProvider.OPENAI:
                    self.providers[provider] = OpenAIProvider(**config)
                elif provider == LLMProvider.ANTHROPIC:
                    self.providers[provider] = AnthropicProvider(**config)
                elif provider == LLMProvider.OLLAMA:
                    self.providers[provider] = OllamaProvider(**config)
                else:
                    self.logger.warning(f"Unknown provider: {provider}")

            except Exception as e:
                self.logger.error(f"Failed to initialize provider {provider}: {e}")

    def _get_provider_config(self, provider: LLMProvider) -> Dict[str, Any]:
        """Get configuration for a provider."""
        if provider == LLMProvider.OPENAI:
            return {
                "api_key": get_config("openai_api_key"),
                "org_id": get_config("openai_org_id"),
                "default_model": get_config("openai_default_model", "gpt-4-turbo"),
            }
        elif provider == LLMProvider.ANTHROPIC:
            return {
                "api_key": get_config("anthropic_api_key"),
                "default_model": get_config("anthropic_default_model", "claude-3-sonnet-20240229"),
            }
        elif provider == LLMProvider.OLLAMA:
            return {
                "base_url": get_config("ollama_base_url", "http://localhost:11434"),
                "default_model": get_config("ollama_default_model", "llama2"),
            }
        else:
            return {}

    async def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        context: Optional[List[Dict[str, str]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate response using the configured provider with fallback."""

        request = LLMRequest(
            prompt=prompt,
            model=model or self.model,
            temperature=temperature or self.temperature,
            max_tokens=max_tokens or self.max_tokens,
            system_prompt=system_prompt,
            context=context,
            tools=tools,
            metadata=kwargs
        )

        # Try primary provider first
        providers_to_try = [self.primary_provider] + self.fallback_providers

        for provider in providers_to_try:
            if provider not in self.providers:
                continue

            try:
                self.logger.debug(f"Attempting generation with {provider.value}")
                response = await self.providers[provider].generate(request)

                if response.success:
                    self.logger.debug(f"Successfully generated response with {provider.value}")
                    return response
                else:
                    self.logger.warning(f"Provider {provider.value} returned error: {response.error}")

            except Exception as e:
                self.logger.error(f"Provider {provider.value} failed: {e}")
                continue

        # All providers failed
        error_msg = "All LLM providers failed to generate response"
        self.logger.error(error_msg)

        return LLMResponse(
            content="",
            model=request.model,
            provider="failed",
            usage={},
            metadata=request.metadata,
            success=False,
            error=error_msg
        )

    async def generate_code(
        self,
        prompt: str,
        language: str = "python",
        context: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate code with specialized prompt."""

        system_prompt = f"""You are an expert {language} programmer. Generate clean, well-documented,
production-ready code that follows best practices and includes proper error handling.

Guidelines:
1. Follow {language} coding standards and conventions
2. Include comprehensive docstrings and comments
3. Add proper error handling and validation
4. Make the code modular and testable
5. Use appropriate design patterns when beneficial

Return only the code without additional explanation unless specifically requested."""

        if context:
            prompt = f"Context: {context}\n\nTask: {prompt}"

        return await self.generate(
            prompt=prompt,
            system_prompt=system_prompt,
            **kwargs
        )

    async def analyze_code(
        self,
        code: str,
        language: str = "python",
        focus: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Analyze code for issues and improvements."""

        system_prompt = f"""You are an expert code reviewer specializing in {language}.
Analyze the provided code for:

1. Code quality and best practices
2. Potential bugs and security issues
3. Performance optimizations
4. Maintainability improvements
5. Documentation quality

Provide specific, actionable feedback with examples where appropriate."""

        prompt = f"Please analyze this {language} code"
        if focus:
            prompt += f" with focus on {focus}"
        prompt += f":\n\n```{language}\n{code}\n```"

        return await self.generate(
            prompt=prompt,
            system_prompt=system_prompt,
            **kwargs
        )

    async def validate_config(self) -> Dict[LLMProvider, bool]:
        """Validate configuration for all providers."""
        results = {}

        for provider, client in self.providers.items():
            try:
                results[provider] = await client.validate_config()
            except Exception as e:
                self.logger.error(f"Config validation failed for {provider}: {e}")
                results[provider] = False

        return results

    def get_available_models(self, provider: Optional[LLMProvider] = None) -> Dict[LLMProvider, List[str]]:
        """Get available models for providers."""
        if provider:
            if provider in self.providers:
                return {provider: self.providers[provider].get_available_models()}
            else:
                return {}

        return {
            p: client.get_available_models()
            for p, client in self.providers.items()
        }

    def get_provider_status(self) -> Dict[str, Any]:
        """Get status of all providers."""
        return {
            "primary_provider": self.primary_provider.value,
            "fallback_providers": [p.value for p in self.fallback_providers],
            "initialized_providers": list(self.providers.keys()),
            "total_providers": len(self.providers),
        }
