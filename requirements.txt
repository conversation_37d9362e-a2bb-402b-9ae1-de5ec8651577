# Core Dependencies
python-dotenv==1.0.0
pydantic==2.5.3
pydantic-settings==2.1.0

# Async Framework
asyncio-mqtt==0.16.1
aiofiles==23.2.1
aioredis==2.0.1

# Web Framework
fastapi==0.108.0
uvicorn[standard]==0.25.0
websockets==12.0
python-socketio==5.11.0
python-multipart==0.0.6

# LLM Providers
openai==1.6.1
anthropic==0.8.1
langchain==0.1.0
langchain-openai==0.0.2
langchain-anthropic==0.0.1
langchain-community==0.0.10

# Multi-Agent Framework
langgraph==0.0.20
langsmith==0.0.77

# Database & Storage
redis==5.0.1
psycopg2-binary==2.9.9
sqlalchemy==2.0.25
alembic==1.13.1

# Vector Database
chromadb==0.4.22
sentence-transformers==2.2.2

# GitHub Integration
PyGithub==2.1.1
gitpython==3.1.40

# Code Analysis & Execution
ast-decompiler==0.7.0
black==23.12.1
flake8==7.0.0
mypy==1.8.0
pytest==7.4.4
pytest-asyncio==0.23.2
pytest-cov==4.1.0
coverage==7.3.4

# Monitoring & Metrics
prometheus-client==0.19.0
structlog==23.2.0

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
httpx==0.26.0
requests==2.31.0
jinja2==3.1.2
pyyaml==6.0.1
toml==0.10.2

# Development Tools
pre-commit==3.6.0
isort==5.13.2
bandit==1.7.5

# Docker Support
docker==7.0.0

# Testing & Mocking
responses==0.24.1
pytest-mock==3.12.0
factory-boy==3.3.0

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# Validation & Serialization
marshmallow==3.20.2
cerberus==1.3.5

# Environment & Config
python-decouple==3.8
configparser==6.0.0

# Logging
loguru==0.7.2

# Security
cryptography>=42.0.0
passlib==1.7.4

# File Processing
pathlib2==2.3.7
watchdog==3.0.0

# Network & HTTP
urllib3==2.1.0
certifi==2023.11.17

# Data Processing
pandas==2.1.4
numpy==1.26.2

# Optional: Local LLM Support
# ollama==0.1.7

# Optional: Advanced Vector DB
# pinecone-client==2.2.4
# weaviate-client==3.25.3

# Optional: Advanced Monitoring
# grafana-api==1.0.3
# elasticsearch==8.11.1
