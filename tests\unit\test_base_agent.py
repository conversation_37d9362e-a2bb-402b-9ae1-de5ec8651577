"""
Unit tests for BaseAgent class in the Multi-Agent Development System.

This module tests the core BaseAgent functionality including initialization,
message handling, task processing, and state management.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from shared.models import (
    Agent<PERSON><PERSON>, AgentStatus, TaskType, TaskStatus, MessageType,
    create_task, create_task_message, create_response_message
)
from shared.utils import AgentEx<PERSON>, TaskException
from agents.base_agent import BaseAgent


class TestBaseAgent:
    """Test BaseAgent class."""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, mock_base_agent):
        """Test agent initialization."""
        agent = mock_base_agent
        
        assert agent.name == "Mock Agent"
        assert agent.role == AgentRole.DEVELOPER
        assert agent.id is not None
        assert agent.status == AgentStatus.OFFLINE
        assert agent.is_running is False
    
    @pytest.mark.asyncio
    async def test_agent_start_stop(self, mock_base_agent):
        """Test agent start and stop functionality."""
        agent = mock_base_agent
        
        # Mock component initialization
        with patch.object(agent, '_initialize_components', new_callable=AsyncMock):
            await agent.start()
            
            assert agent.is_running is True
            assert agent.status == AgentStatus.INITIALIZING
            assert agent._start_time is not None
        
        # Stop agent
        await agent.stop()
        assert agent.is_running is False
    
    @pytest.mark.asyncio
    async def test_message_handling(self, mock_base_agent, sample_message):
        """Test message handling."""
        agent = mock_base_agent
        
        # Test direct message handling
        response = await agent.handle_message(sample_message)
        
        # Should handle message without error
        assert agent.agent.metrics.messages_received == 1
    
    @pytest.mark.asyncio
    async def test_task_message_handling(self, mock_base_agent):
        """Test task message handling."""
        agent = mock_base_agent
        
        # Create task message
        task_data = {
            "title": "Test Task",
            "description": "Test task description",
            "task_type": TaskType.DEVELOPMENT.value,
            "created_by": "test_pm",
            "created_by_role": AgentRole.PROJECT_MANAGER.value
        }
        
        task_message = create_task_message(
            sender_id="pm_agent",
            sender_role=AgentRole.PROJECT_MANAGER,
            task_id="test_task",
            action="execute",
            receiver_id=agent.id,
            receiver_role=agent.role
        )
        task_message.metadata.context["task_data"] = task_data
        
        # Handle task message
        response = await agent.handle_message(task_message)
        
        assert response is not None
        assert response.message_type == MessageType.RESPONSE
    
    @pytest.mark.asyncio
    async def test_task_assignment(self, mock_base_agent, sample_task):
        """Test task assignment."""
        agent = mock_base_agent
        
        # Mock agent availability
        agent.agent.set_idle()
        
        # Assign task
        success = await agent.assign_task(sample_task)
        
        assert success is True
        assert sample_task.assigned_to == agent.id
        assert sample_task.status == TaskStatus.ASSIGNED
    
    @pytest.mark.asyncio
    async def test_task_assignment_when_busy(self, mock_base_agent, sample_task):
        """Test task assignment when agent is busy."""
        agent = mock_base_agent
        
        # Set agent as working
        agent.agent.set_working("other_task")
        
        # Try to assign task
        success = await agent.assign_task(sample_task)
        
        # Should fail because agent is busy
        assert success is False
    
    @pytest.mark.asyncio
    async def test_task_processing(self, mock_base_agent, sample_task):
        """Test task processing."""
        agent = mock_base_agent
        
        # Set agent as idle
        agent.agent.set_idle()
        
        # Process task internally
        await agent._process_task_internal(sample_task)
        
        # Check that task was processed
        assert sample_task.id in agent.processed_tasks[0].id
        assert sample_task.status == TaskStatus.COMPLETED
        assert agent.agent.state.status == AgentStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_task_processing_failure(self, mock_base_agent, sample_task):
        """Test task processing failure handling."""
        agent = mock_base_agent
        
        # Mock process_task to raise exception
        async def failing_process_task(task):
            raise Exception("Task processing failed")
        
        agent.process_task = failing_process_task
        
        # Set agent as idle
        agent.agent.set_idle()
        
        # Process task internally
        await agent._process_task_internal(sample_task)
        
        # Check that task failed
        assert sample_task.status == TaskStatus.FAILED
        assert sample_task.result.success is False
        assert "Task processing failed" in sample_task.result.error
    
    @pytest.mark.asyncio
    async def test_heartbeat_functionality(self, mock_base_agent):
        """Test heartbeat functionality."""
        agent = mock_base_agent
        
        # Send heartbeat
        await agent._send_heartbeat()
        
        # Check that heartbeat was updated
        assert agent.agent.state.last_heartbeat is not None
        assert agent._last_heartbeat is not None
    
    @pytest.mark.asyncio
    async def test_message_sending(self, mock_base_agent, mock_communication_hub, sample_message):
        """Test message sending."""
        agent = mock_base_agent
        agent.communication_hub = mock_communication_hub
        
        # Send message
        success = await agent.send_message(sample_message)
        
        assert success is True
        assert agent.agent.metrics.messages_sent == 1
        mock_communication_hub.send_message.assert_called_once_with(sample_message)
    
    @pytest.mark.asyncio
    async def test_message_sending_without_hub(self, mock_base_agent, sample_message):
        """Test message sending without communication hub."""
        agent = mock_base_agent
        agent.communication_hub = None
        
        # Send message
        success = await agent.send_message(sample_message)
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_notification_sending(self, mock_base_agent, mock_communication_hub):
        """Test notification sending."""
        agent = mock_base_agent
        agent.communication_hub = mock_communication_hub
        
        # Send notification
        await agent.notify_agents("Test notification", [AgentRole.DEVELOPER])
        
        # Check that message was sent
        mock_communication_hub.send_message.assert_called_once()
        
        # Get the sent message
        sent_message = mock_communication_hub.send_message.call_args[0][0]
        assert sent_message.message_type == MessageType.NOTIFICATION
        assert sent_message.content == "Test notification"
    
    def test_status_info(self, mock_base_agent):
        """Test status info retrieval."""
        agent = mock_base_agent
        
        status_info = agent.get_status_info()
        
        assert "agent_id" in status_info
        assert "name" in status_info
        assert "role" in status_info
        assert "status" in status_info
        assert "metrics" in status_info
        
        assert status_info["agent_id"] == agent.id
        assert status_info["name"] == agent.name
        assert status_info["role"] == agent.role.value
    
    @pytest.mark.asyncio
    async def test_active_task_completion_on_shutdown(self, mock_base_agent):
        """Test active task completion during shutdown."""
        agent = mock_base_agent
        
        # Add some active tasks
        task1 = create_task(
            title="Active Task 1",
            description="Test task 1",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_pm",
            created_by_role=AgentRole.PROJECT_MANAGER
        )
        task1.start()
        agent._active_tasks[task1.id] = task1
        
        task2 = create_task(
            title="Active Task 2", 
            description="Test task 2",
            task_type=TaskType.TESTING,
            created_by="test_pm",
            created_by_role=AgentRole.PROJECT_MANAGER
        )
        task2.start()
        agent._active_tasks[task2.id] = task2
        
        # Complete active tasks
        await agent._complete_active_tasks()
        
        # Check that tasks were cancelled
        assert task1.status == TaskStatus.CANCELLED
        assert task2.status == TaskStatus.CANCELLED
        assert len(agent._active_tasks) == 0
    
    def test_string_representations(self, mock_base_agent):
        """Test string representations of agent."""
        agent = mock_base_agent
        
        str_repr = str(agent)
        assert agent.name in str_repr
        assert agent.role.value in str_repr
        
        repr_str = repr(agent)
        assert "BaseAgent" in repr_str
        assert agent.id in repr_str
        assert agent.name in repr_str


class TestBaseAgentErrorHandling:
    """Test BaseAgent error handling."""
    
    @pytest.mark.asyncio
    async def test_initialization_error_handling(self, sample_agent_config, sample_agent_capabilities):
        """Test error handling during initialization."""
        
        class FailingAgent(BaseAgent):
            async def process_task(self, task):
                pass
            
            async def get_capabilities(self):
                return self.agent.capabilities
            
            async def _initialize_components(self):
                raise Exception("Initialization failed")
        
        agent = FailingAgent(
            name="Failing Agent",
            role=AgentRole.DEVELOPER,
            description="Agent that fails to initialize",
            capabilities=sample_agent_capabilities,
            configuration=sample_agent_config
        )
        
        # Starting should raise an exception
        with pytest.raises(AgentException):
            await agent.start()
    
    @pytest.mark.asyncio
    async def test_message_handling_error_recovery(self, mock_base_agent):
        """Test error recovery in message handling."""
        agent = mock_base_agent
        
        # Create a message that will cause an error
        bad_message = Mock()
        bad_message.id = "bad_message"
        bad_message.message_type = MessageType.TASK
        bad_message.sender_id = "sender"
        
        # Mock the handler to raise an exception
        async def failing_handler(message):
            raise Exception("Handler failed")
        
        agent._message_handlers[MessageType.TASK] = failing_handler
        
        # Handle message - should not raise exception
        response = await agent.handle_message(bad_message)
        
        # Should return None and log error
        assert response is None
    
    @pytest.mark.asyncio
    async def test_task_queue_error_handling(self, mock_base_agent):
        """Test error handling in task queue processing."""
        agent = mock_base_agent
        agent._running = True
        
        # Mock task queue to raise exception
        async def failing_get():
            raise Exception("Queue error")
        
        agent._task_queue.get = failing_get
        
        # Start task processor - should handle error gracefully
        # This is a bit tricky to test without actually running the loop
        # In a real scenario, we'd need to mock the entire event loop
        pass  # Placeholder for more complex async testing


class TestBaseAgentIntegration:
    """Integration tests for BaseAgent."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_agent_lifecycle(self, mock_base_agent, sample_task):
        """Test full agent lifecycle with task processing."""
        agent = mock_base_agent
        
        # Mock component initialization
        with patch.object(agent, '_initialize_components', new_callable=AsyncMock):
            # Start agent
            await agent.start()
            assert agent.is_running is True
            
            # Assign and process task
            agent.agent.set_idle()
            success = await agent.assign_task(sample_task)
            assert success is True
            
            # Wait a bit for task processing
            await asyncio.sleep(0.1)
            
            # Stop agent
            await agent.stop()
            assert agent.is_running is False
