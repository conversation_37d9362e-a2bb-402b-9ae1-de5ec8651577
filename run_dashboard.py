#!/usr/bin/env python3
"""
Simple Dashboard Runner for Multi-Agent Development System.

This script runs a basic dashboard without requiring Redis or external services.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.responses import HTMLResponse
import uvicorn

# Create a simple FastAPI app
app = FastAPI(
    title="Multi-Agent System Dashboard",
    description="Basic dashboard for the multi-agent development system",
    version="1.0.0"
)

@app.get("/", response_class=HTMLResponse)
async def dashboard_home():
    """Main dashboard page."""
    html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Multi-Agent Development System</title>
        <style>
            body {
                font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 800px;
                margin: 20px;
            }
            h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 2.5em;
            }
            .subtitle {
                color: #666;
                font-size: 1.2em;
                margin-bottom: 30px;
            }
            .status {
                background: #e8f5e8;
                color: #2d5a2d;
                padding: 15px;
                border-radius: 10px;
                margin: 20px 0;
                border-left: 5px solid #4caf50;
            }
            .features {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }
            .feature {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 10px;
                border: 1px solid #e9ecef;
            }
            .feature h3 {
                color: #495057;
                margin-bottom: 10px;
            }
            .feature p {
                color: #6c757d;
                margin: 0;
                font-size: 0.9em;
            }
            .api-links {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e9ecef;
            }
            .api-link {
                display: inline-block;
                margin: 5px 10px;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                transition: background 0.3s;
            }
            .api-link:hover {
                background: #0056b3;
            }
            .icon {
                font-size: 2em;
                margin-bottom: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 Multi-Agent Development System</h1>
            <p class="subtitle">Intelligent AI Agents for Software Development</p>
            
            <div class="status">
                ✅ System is running and ready for development tasks!
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="icon">👨‍💼</div>
                    <h3>Project Manager Agent</h3>
                    <p>Handles project planning, task coordination, and team management</p>
                </div>
                
                <div class="feature">
                    <div class="icon">👨‍💻</div>
                    <h3>Developer Agent</h3>
                    <p>Generates code, manages files, and handles development tasks</p>
                </div>
                
                <div class="feature">
                    <div class="icon">🧪</div>
                    <h3>Test Agent</h3>
                    <p>Creates tests, runs quality checks, and ensures code reliability</p>
                </div>
                
                <div class="feature">
                    <div class="icon">🔄</div>
                    <h3>Communication Hub</h3>
                    <p>Manages inter-agent messaging and coordination</p>
                </div>
                
                <div class="feature">
                    <div class="icon">📊</div>
                    <h3>Real-time Monitoring</h3>
                    <p>WebSocket-based live updates and system monitoring</p>
                </div>
                
                <div class="feature">
                    <div class="icon">🛠️</div>
                    <h3>Tool Integration</h3>
                    <p>GitHub, file management, code analysis, and more</p>
                </div>
            </div>
            
            <div class="api-links">
                <h3>API Endpoints</h3>
                <a href="/health" class="api-link">Health Check</a>
                <a href="/system-info" class="api-link">System Info</a>
                <a href="/docs" class="api-link">API Documentation</a>
            </div>
            
            <div style="margin-top: 30px; color: #6c757d; font-size: 0.9em;">
                <p>🚀 Ready to build amazing software with AI agents!</p>
                <p>Version 1.0.0 | Built with FastAPI & Python</p>
            </div>
        </div>
    </body>
    </html>
    """
    return html

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        from shared.models import AgentRole, TaskType, MessageType
        from shared.utils import get_config
        
        return {
            "status": "healthy",
            "message": "Multi-Agent Development System is running",
            "version": "1.0.0",
            "components": {
                "models": "✅ Available",
                "utils": "✅ Available", 
                "agents": "✅ Available",
                "communication": "✅ Available"
            },
            "timestamp": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2024-01-01T00:00:00Z"
        }

@app.get("/system-info")
async def system_info():
    """Get system information."""
    try:
        from shared.models import AgentRole, TaskType, MessageType
        
        return {
            "system": "Multi-Agent Development System",
            "version": "1.0.0",
            "author": "inkbytefo",
            "description": "AI-powered multi-agent system for software development",
            "features": [
                "Project Manager Agent - Planning & Coordination",
                "Developer Agent - Code Generation & Management", 
                "Test Agent - Quality Assurance & Testing",
                "Communication Hub - Inter-agent Messaging",
                "WebSocket Server - Real-time Updates",
                "Tool Integration - GitHub, File Management, etc."
            ],
            "supported_roles": [role.value for role in AgentRole],
            "supported_task_types": [task_type.value for task_type in TaskType],
            "supported_message_types": [msg_type.value for msg_type in MessageType],
            "architecture": {
                "async_framework": "asyncio",
                "web_framework": "FastAPI",
                "data_models": "Pydantic",
                "communication": "Redis + WebSocket",
                "llm_providers": ["OpenAI", "Anthropic", "Ollama"]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/test-models")
async def test_models():
    """Test model creation."""
    try:
        from shared.models import (
            Agent, Task, Message, AgentRole, TaskType, MessageType,
            AgentCapabilities, AgentConfiguration, LLMProvider,
            create_task, create_message
        )
        
        # Test agent creation
        capabilities = AgentCapabilities(
            skills=["python", "testing"],
            tools=[],
            languages=["python"],
            frameworks=["pytest"],
            max_concurrent_tasks=2
        )
        
        configuration = AgentConfiguration(
            llm_provider=LLMProvider.OPENAI,
            model_name="gpt-4",
            temperature=0.1
        )
        
        agent = Agent(
            name="Test Agent",
            role=AgentRole.DEVELOPER,
            description="Test agent for API validation",
            capabilities=capabilities,
            configuration=configuration
        )
        
        # Test task creation
        task = create_task(
            title="Test API Task",
            description="Test task created via API",
            task_type=TaskType.DEVELOPMENT,
            created_by=agent.id,
            created_by_role=agent.role
        )
        
        # Test message creation
        message = create_message(
            sender_id=agent.id,
            sender_role=agent.role,
            receiver_id="test_receiver",
            receiver_role=AgentRole.PROJECT_MANAGER,
            message_type=MessageType.NOTIFICATION,
            content="Test message from API"
        )
        
        return {
            "status": "success",
            "message": "All models created successfully",
            "created": {
                "agent": {
                    "id": agent.id,
                    "name": agent.name,
                    "role": agent.role
                },
                "task": {
                    "id": task.id,
                    "title": task.title,
                    "type": task.task_type
                },
                "message": {
                    "id": message.id,
                    "type": message.message_type,
                    "content": message.content[:50] + "..."
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """Run the dashboard."""
    print("🚀 Starting Multi-Agent Development System Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8000")
    print("📚 API Documentation at: http://localhost:8000/docs")
    print("❤️  Health Check at: http://localhost:8000/health")
    print("\n✨ Press Ctrl+C to stop the server")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n⏹️  Dashboard stopped by user")
    except Exception as e:
        print(f"\n💥 Dashboard error: {e}")

if __name__ == "__main__":
    main()
