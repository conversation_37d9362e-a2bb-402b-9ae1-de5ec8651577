"""
Web Dashboard for the Multi-Agent Development System.

This module provides a FastAPI-based web dashboard for monitoring and
controlling the multi-agent system.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import <PERSON>AP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse
import uvicorn

from shared.models import Agent<PERSON><PERSON>, MessageType
from shared.utils import get_logger, get_config
from shared.communication import CommunicationHub, RedisClient
from shared.communication.websocket_server import WebSocketServer


class DashboardApp:
    """Main dashboard application."""
    
    def __init__(self):
        """Initialize dashboard application."""
        self.logger = get_logger("dashboard")
        
        # FastAPI app
        self.app = FastAPI(
            title="Multi-Agent Development System Dashboard",
            description="Real-time monitoring and control dashboard",
            version="1.0.0"
        )
        
        # Configuration
        self.host = get_config("api_host", "localhost")
        self.port = get_config("api_port", 8080)
        self.debug = get_config("api_debug", True)
        
        # Components
        self.redis_client = RedisClient()
        self.communication_hub = CommunicationHub(self.redis_client)
        self.websocket_server = WebSocketServer(self.communication_hub)
        
        # Templates and static files
        self.templates = Jinja2Templates(directory="dashboard/templates")
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("Dashboard application initialized")
    
    def _setup_routes(self) -> None:
        """Setup FastAPI routes."""
        
        # Static files
        self.app.mount("/static", StaticFiles(directory="dashboard/static"), name="static")
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """Main dashboard page."""
            try:
                # Get system statistics
                stats = self.communication_hub.get_statistics()
                agents = self.communication_hub.get_registered_agents()
                
                return self.templates.TemplateResponse("index.html", {
                    "request": request,
                    "title": "Multi-Agent System Dashboard",
                    "stats": stats,
                    "agents": agents,
                    "timestamp": datetime.utcnow().isoformat()
                })
                
            except Exception as e:
                self.logger.error(f"Error rendering dashboard: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/agents", response_class=HTMLResponse)
        async def agents_page(request: Request):
            """Agents monitoring page."""
            try:
                agents = self.communication_hub.get_registered_agents()
                
                return self.templates.TemplateResponse("agents.html", {
                    "request": request,
                    "title": "Agents - Multi-Agent System",
                    "agents": agents
                })
                
            except Exception as e:
                self.logger.error(f"Error rendering agents page: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/messages", response_class=HTMLResponse)
        async def messages_page(request: Request):
            """Messages monitoring page."""
            try:
                messages = self.communication_hub.get_message_history(limit=100)
                message_data = [msg.to_dict() for msg in messages]
                
                return self.templates.TemplateResponse("messages.html", {
                    "request": request,
                    "title": "Messages - Multi-Agent System",
                    "messages": message_data
                })
                
            except Exception as e:
                self.logger.error(f"Error rendering messages page: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # API Routes
        @self.app.get("/api/health")
        async def health_check():
            """Health check endpoint."""
            try:
                hub_health = await self.communication_hub.health_check()
                redis_health = await self.redis_client.health_check()
                ws_stats = self.websocket_server.get_statistics()
                
                return {
                    "status": "healthy",
                    "timestamp": datetime.utcnow().isoformat(),
                    "components": {
                        "communication_hub": hub_health,
                        "redis": redis_health,
                        "websocket_server": ws_stats
                    }
                }
                
            except Exception as e:
                self.logger.error(f"Health check failed: {e}")
                return {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
        
        @self.app.get("/api/agents")
        async def get_agents():
            """Get all registered agents."""
            try:
                agents = self.communication_hub.get_registered_agents()
                return {"agents": agents}
                
            except Exception as e:
                self.logger.error(f"Error getting agents: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/agents/{agent_id}")
        async def get_agent(agent_id: str):
            """Get specific agent information."""
            try:
                agents = self.communication_hub.get_registered_agents()
                
                if agent_id not in agents:
                    raise HTTPException(status_code=404, detail="Agent not found")
                
                return {"agent": agents[agent_id]}
                
            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(f"Error getting agent {agent_id}: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/statistics")
        async def get_statistics():
            """Get system statistics."""
            try:
                stats = self.communication_hub.get_statistics()
                return {"statistics": stats}
                
            except Exception as e:
                self.logger.error(f"Error getting statistics: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/messages")
        async def get_messages(limit: int = 50):
            """Get message history."""
            try:
                messages = self.communication_hub.get_message_history(limit=limit)
                message_data = [msg.to_dict() for msg in messages]
                
                return {"messages": message_data}
                
            except Exception as e:
                self.logger.error(f"Error getting messages: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/messages/send")
        async def send_message(message_data: Dict[str, Any]):
            """Send a message through the system."""
            try:
                from shared.models import Message
                
                # Create message
                message = Message(
                    sender_id=message_data.get("sender_id", "dashboard"),
                    sender_role=AgentRole.COORDINATOR,
                    receiver_id=message_data.get("receiver_id"),
                    message_type=MessageType(message_data.get("message_type", "notification")),
                    content=message_data.get("content", "")
                )
                
                # Send message
                success = await self.communication_hub.send_message(message)
                
                return {"success": success, "message_id": message.id}
                
            except Exception as e:
                self.logger.error(f"Error sending message: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # WebSocket endpoint for real-time updates
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await websocket.accept()
            
            try:
                # Send initial data
                agents = self.communication_hub.get_registered_agents()
                stats = self.communication_hub.get_statistics()
                
                await websocket.send_json({
                    "type": "initial_data",
                    "data": {
                        "agents": agents,
                        "statistics": stats
                    }
                })
                
                # Keep connection alive and send updates
                while True:
                    try:
                        # Wait for client message or timeout
                        data = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)
                        
                        # Handle client requests
                        if data.get("type") == "get_agents":
                            agents = self.communication_hub.get_registered_agents()
                            await websocket.send_json({
                                "type": "agents_update",
                                "data": {"agents": agents}
                            })
                        
                        elif data.get("type") == "get_statistics":
                            stats = self.communication_hub.get_statistics()
                            await websocket.send_json({
                                "type": "statistics_update",
                                "data": {"statistics": stats}
                            })
                            
                    except asyncio.TimeoutError:
                        # Send periodic updates
                        stats = self.communication_hub.get_statistics()
                        await websocket.send_json({
                            "type": "statistics_update",
                            "data": {"statistics": stats}
                        })
                        
            except WebSocketDisconnect:
                self.logger.info("WebSocket client disconnected")
            except Exception as e:
                self.logger.error(f"WebSocket error: {e}")
                await websocket.close()
    
    async def start(self) -> None:
        """Start the dashboard application."""
        try:
            self.logger.info("Starting dashboard application")
            
            # Start communication components
            await self.communication_hub.start()
            await self.websocket_server.start()
            
            self.logger.info(f"Dashboard started on http://{self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start dashboard: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the dashboard application."""
        try:
            self.logger.info("Stopping dashboard application")
            
            # Stop communication components
            await self.websocket_server.stop()
            await self.communication_hub.stop()
            
            self.logger.info("Dashboard stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping dashboard: {e}")
    
    def run(self) -> None:
        """Run the dashboard application."""
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            reload=self.debug,
            log_level="info"
        )


# Global dashboard instance
dashboard = DashboardApp()

# FastAPI app for external access
app = dashboard.app


async def main():
    """Main entry point for running the dashboard."""
    try:
        await dashboard.start()
        
        # Run the server
        config = uvicorn.Config(
            app=dashboard.app,
            host=dashboard.host,
            port=dashboard.port,
            reload=dashboard.debug,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        dashboard.logger.info("Received shutdown signal")
    finally:
        await dashboard.stop()


if __name__ == "__main__":
    asyncio.run(main())
