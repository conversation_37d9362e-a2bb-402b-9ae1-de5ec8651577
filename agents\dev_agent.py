"""
Developer Agent for the Multi-Agent Development System.

This module implements the Developer Agent responsible for code generation,
file management, and software development tasks in the multi-agent environment.
"""

import asyncio
import os
import re
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from shared.models import (
    AgentRole, AgentCapabilities, AgentConfiguration, TaskType, TaskPriority,
    TaskStatus, Task, TaskResult, Message, MessageType, LLMProvider, ToolType,
    create_task, create_response_message, create_notification_message
)
from shared.utils import get_agent_logger, log_agent_action, get_config
from shared.llm import LLMClient
from shared.tools import ToolManager
from .base_agent import BaseAgent


class DeveloperAgent(BaseAgent):
    """
    Developer Agent responsible for:
    - Code generation and implementation
    - File and directory management
    - Code review and refactoring
    - Documentation generation
    - Git operations and version control
    - Debugging and troubleshooting
    """
    
    def __init__(self, name: str = "Dev Agent", **kwargs):
        """Initialize Developer Agent."""
        
        # Define Dev-specific capabilities
        capabilities = AgentCapabilities(
            skills=[
                "python_development", "javascript_development", "web_development",
                "api_development", "database_design", "code_review",
                "debugging", "refactoring", "documentation", "git_operations",
                "testing", "performance_optimization"
            ],
            tools=[
                ToolType.CODE_GENERATOR, ToolType.FILE_MANAGER, ToolType.GITHUB_CLIENT,
                ToolType.DEBUGGER, ToolType.CODE_ANALYZER, ToolType.DOCUMENTATION_GENERATOR
            ],
            languages=[
                "python", "javascript", "typescript", "html", "css", "sql",
                "bash", "yaml", "json", "markdown"
            ],
            frameworks=[
                "fastapi", "flask", "django", "react", "vue", "express",
                "pytest", "jest", "sqlalchemy", "pydantic"
            ],
            max_concurrent_tasks=3
        )
        
        # Define Dev-specific configuration
        configuration = AgentConfiguration(
            llm_provider=LLMProvider.ANTHROPIC,
            model_name=get_config("dev_agent_model", "claude-3-sonnet-20240229"),
            temperature=get_config("dev_agent_temperature", 0.1),
            max_tokens=get_config("dev_agent_max_tokens", 4000),
            timeout_seconds=45,
            max_retries=3,
            enable_memory=True,
            enable_tools=True,
            enable_collaboration=True
        )
        
        super().__init__(
            name=name,
            role=AgentRole.DEVELOPER,
            description="Developer Agent responsible for code generation, file management, and software development",
            capabilities=capabilities,
            configuration=configuration,
            **kwargs
        )
        
        # Dev-specific state
        self.current_project_path: Optional[str] = None
        self.code_templates: Dict[str, str] = {}
        self.file_history: List[Dict[str, Any]] = []
        
        # Initialize code templates
        self._initialize_code_templates()
        
        self.logger.info("Developer Agent initialized")
    
    def _initialize_code_templates(self) -> None:
        """Initialize code templates for common development patterns."""
        self.code_templates = {
            "fastapi_app": '''"""
{description}
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

app = FastAPI(
    title="{title}",
    description="{description}",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {{"message": "Welcome to {title}"}}

@app.get("/health")
async def health_check():
    return {{"status": "healthy", "timestamp": "{{datetime.utcnow().isoformat()}}"}}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
''',
            
            "pydantic_model": '''"""
{description}
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class {class_name}(BaseModel):
    """
    {description}
    """
    
    id: Optional[str] = Field(None, description="Unique identifier")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {{
            datetime: lambda v: v.isoformat(),
        }}
        use_enum_values = True
    
    @validator('updated_at', always=True)
    def set_updated_at(cls, v):
        """Always update the updated_at timestamp."""
        return datetime.utcnow()
''',
            
            "pytest_test": '''"""
Tests for {module_name}.

This module contains unit tests for the {module_name} functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from {module_path} import {class_name}

class Test{class_name}:
    """Test {class_name} class."""
    
    def test_initialization(self):
        """Test {class_name} initialization."""
        # Test implementation here
        pass
    
    @pytest.mark.asyncio
    async def test_async_method(self):
        """Test async methods."""
        # Test implementation here
        pass
    
    def test_edge_cases(self):
        """Test edge cases and error handling."""
        # Test implementation here
        pass
''',
            
            "requirements_txt": '''# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.13.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Optional dependencies
redis==5.0.1
httpx==0.25.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
''',
            
            "dockerfile": '''FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
'''
        }
    
    async def process_task(self, task: Task) -> TaskResult:
        """Process Dev-specific tasks."""
        try:
            self.logger.info(f"Processing Dev task: {task.title}")
            
            # Determine task type and route to appropriate handler
            task_lower = task.title.lower()
            description_lower = task.description.lower()
            
            if any(keyword in task_lower for keyword in ["code", "implement", "develop", "create"]):
                return await self._handle_code_task(task)
            elif any(keyword in task_lower for keyword in ["file", "directory", "folder"]):
                return await self._handle_file_task(task)
            elif any(keyword in task_lower for keyword in ["review", "analyze", "refactor"]):
                return await self._handle_review_task(task)
            elif any(keyword in task_lower for keyword in ["debug", "fix", "error"]):
                return await self._handle_debug_task(task)
            elif any(keyword in task_lower for keyword in ["test", "testing"]):
                return await self._handle_test_task(task)
            elif any(keyword in task_lower for keyword in ["document", "docs"]):
                return await self._handle_documentation_task(task)
            else:
                return await self._handle_general_dev_task(task)
                
        except Exception as e:
            self.logger.error(f"Error processing Dev task: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Dev task processing failed: {e}"]
            )
    
    async def _handle_code_task(self, task: Task) -> TaskResult:
        """Handle code generation and implementation tasks."""
        try:
            self.logger.info(f"Handling code task: {task.title}")
            
            # Extract code requirements
            language = task.get_context("language", "python")
            framework = task.get_context("framework", "fastapi")
            file_path = task.get_context("file_path", "")
            code_type = task.get_context("code_type", "general")
            
            # Generate code using LLM
            code_prompt = f"""
            As a Senior Developer, implement the following: {task.title}
            
            Requirements: {task.description}
            Language: {language}
            Framework: {framework}
            File Path: {file_path}
            Code Type: {code_type}
            
            Please provide:
            1. Clean, well-documented code
            2. Proper error handling
            3. Type hints (for Python)
            4. Docstrings and comments
            5. Follow best practices and conventions
            
            Generate only the code without additional explanation.
            """
            
            llm_response = await self.llm_client.generate_code(
                prompt=code_prompt,
                language=language,
                context=f"Framework: {framework}, Type: {code_type}"
            )
            
            if not llm_response.success:
                raise Exception(f"Code generation failed: {llm_response.error}")
            
            generated_code = llm_response.content
            
            # Save code to file if path specified
            if file_path and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="write",
                    path=file_path,
                    content=generated_code
                )
                
                if file_result.success:
                    self._add_to_file_history("created", file_path, len(generated_code))
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "code_generated",
                {
                    "language": language,
                    "framework": framework,
                    "file_path": file_path,
                    "code_length": len(generated_code)
                }
            )
            
            return TaskResult(
                success=True,
                output={
                    "generated_code": generated_code,
                    "language": language,
                    "framework": framework,
                    "file_path": file_path,
                    "code_length": len(generated_code)
                },
                logs=[f"Code generated for {task.title}"],
                duration_seconds=3.0
            )
            
        except Exception as e:
            self.logger.error(f"Code task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Code generation failed: {e}"]
            )
    
    async def _handle_file_task(self, task: Task) -> TaskResult:
        """Handle file and directory management tasks."""
        try:
            self.logger.info(f"Handling file task: {task.title}")
            
            operation = task.get_context("operation", "create")
            path = task.get_context("path", "")
            content = task.get_context("content", "")
            
            if not path:
                # Extract path from task description
                path_match = re.search(r'["\']([^"\']+)["\']', task.description)
                if path_match:
                    path = path_match.group(1)
                else:
                    raise Exception("No file path specified")
            
            # Execute file operation using tool manager
            if self.tool_manager:
                if operation in ["create", "write"]:
                    result = await self.tool_manager.execute_tool(
                        "file_manager",
                        operation="write",
                        path=path,
                        content=content or f"# {task.title}\n\n# Created by Dev Agent\n"
                    )
                elif operation == "read":
                    result = await self.tool_manager.execute_tool(
                        "file_manager",
                        operation="read",
                        path=path
                    )
                elif operation == "list":
                    result = await self.tool_manager.execute_tool(
                        "file_manager",
                        operation="list",
                        path=path
                    )
                elif operation == "create_dir":
                    result = await self.tool_manager.execute_tool(
                        "file_manager",
                        operation="create_dir",
                        path=path
                    )
                else:
                    raise Exception(f"Unknown file operation: {operation}")
                
                if result.success:
                    self._add_to_file_history(operation, path, len(str(result.output)))
                
                return TaskResult(
                    success=result.success,
                    output=result.output,
                    error=result.error,
                    logs=[f"File operation {operation} completed on {path}"],
                    duration_seconds=1.0
                )
            else:
                raise Exception("Tool manager not available")
                
        except Exception as e:
            self.logger.error(f"File task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"File operation failed: {e}"]
            )
    
    async def _handle_review_task(self, task: Task) -> TaskResult:
        """Handle code review and analysis tasks."""
        try:
            self.logger.info(f"Handling review task: {task.title}")
            
            # Get code to review
            code_path = task.get_context("code_path", "")
            code_content = task.get_context("code_content", "")
            review_focus = task.get_context("focus", "general")
            
            # Read code from file if path provided
            if code_path and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="read",
                    path=code_path
                )
                if file_result.success:
                    code_content = file_result.output
            
            if not code_content:
                raise Exception("No code content provided for review")
            
            # Analyze code using LLM
            llm_response = await self.llm_client.analyze_code(
                code=code_content,
                language="python",  # Default to Python, could be detected
                focus=review_focus
            )
            
            if not llm_response.success:
                raise Exception(f"Code analysis failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "review_report": llm_response.content,
                    "code_path": code_path,
                    "review_focus": review_focus,
                    "code_length": len(code_content)
                },
                logs=[f"Code review completed for {task.title}"],
                duration_seconds=2.5
            )
            
        except Exception as e:
            self.logger.error(f"Review task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Code review failed: {e}"]
            )
    
    async def _handle_debug_task(self, task: Task) -> TaskResult:
        """Handle debugging and troubleshooting tasks."""
        try:
            self.logger.info(f"Handling debug task: {task.title}")
            
            error_message = task.get_context("error", "")
            code_context = task.get_context("code", "")
            stack_trace = task.get_context("stack_trace", "")
            
            # Generate debugging analysis
            debug_prompt = f"""
            As a Senior Developer, debug the following issue: {task.title}
            
            Problem Description: {task.description}
            Error Message: {error_message}
            Code Context: {code_context}
            Stack Trace: {stack_trace}
            
            Please provide:
            1. Root cause analysis
            2. Step-by-step debugging approach
            3. Potential solutions
            4. Code fixes if applicable
            5. Prevention strategies
            """
            
            llm_response = await self.llm_client.generate(
                prompt=debug_prompt,
                system_prompt="You are a senior developer with expertise in debugging and troubleshooting."
            )
            
            if not llm_response.success:
                raise Exception(f"Debug analysis failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "debug_analysis": llm_response.content,
                    "error_message": error_message,
                    "has_code_context": bool(code_context),
                    "has_stack_trace": bool(stack_trace)
                },
                logs=[f"Debug analysis completed for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Debug task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Debugging failed: {e}"]
            )
    
    async def _handle_test_task(self, task: Task) -> TaskResult:
        """Handle test generation and execution tasks."""
        try:
            self.logger.info(f"Handling test task: {task.title}")
            
            test_type = task.get_context("test_type", "unit")
            target_code = task.get_context("target_code", "")
            test_framework = task.get_context("framework", "pytest")
            
            # Generate test code
            test_prompt = f"""
            As a Senior Developer, create {test_type} tests for: {task.title}
            
            Requirements: {task.description}
            Target Code: {target_code}
            Test Framework: {test_framework}
            
            Please provide:
            1. Comprehensive test cases
            2. Edge case testing
            3. Error condition testing
            4. Mock usage where appropriate
            5. Clear test documentation
            
            Generate complete, runnable test code.
            """
            
            llm_response = await self.llm_client.generate_code(
                prompt=test_prompt,
                language="python",
                context=f"Test framework: {test_framework}, Type: {test_type}"
            )
            
            if not llm_response.success:
                raise Exception(f"Test generation failed: {llm_response.error}")
            
            test_code = llm_response.content
            
            # Save test file if requested
            test_file_path = task.get_context("test_file_path", "")
            if test_file_path and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="write",
                    path=test_file_path,
                    content=test_code
                )
                
                if file_result.success:
                    self._add_to_file_history("created", test_file_path, len(test_code))
            
            return TaskResult(
                success=True,
                output={
                    "test_code": test_code,
                    "test_type": test_type,
                    "test_framework": test_framework,
                    "test_file_path": test_file_path
                },
                logs=[f"Test code generated for {task.title}"],
                duration_seconds=2.5
            )
            
        except Exception as e:
            self.logger.error(f"Test task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Test generation failed: {e}"]
            )
    
    async def _handle_documentation_task(self, task: Task) -> TaskResult:
        """Handle documentation generation tasks."""
        try:
            self.logger.info(f"Handling documentation task: {task.title}")
            
            doc_type = task.get_context("doc_type", "readme")
            target_code = task.get_context("target_code", "")
            format_type = task.get_context("format", "markdown")
            
            # Generate documentation
            doc_prompt = f"""
            As a Senior Developer, create {doc_type} documentation for: {task.title}
            
            Requirements: {task.description}
            Target Code/Project: {target_code}
            Format: {format_type}
            
            Please provide:
            1. Clear and comprehensive documentation
            2. Usage examples
            3. Installation instructions (if applicable)
            4. API documentation (if applicable)
            5. Contributing guidelines (if applicable)
            
            Generate well-structured {format_type} documentation.
            """
            
            llm_response = await self.llm_client.generate(
                prompt=doc_prompt,
                system_prompt="You are a technical writer creating clear, comprehensive documentation."
            )
            
            if not llm_response.success:
                raise Exception(f"Documentation generation failed: {llm_response.error}")
            
            documentation = llm_response.content
            
            # Save documentation file if requested
            doc_file_path = task.get_context("doc_file_path", "")
            if doc_file_path and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="write",
                    path=doc_file_path,
                    content=documentation
                )
                
                if file_result.success:
                    self._add_to_file_history("created", doc_file_path, len(documentation))
            
            return TaskResult(
                success=True,
                output={
                    "documentation": documentation,
                    "doc_type": doc_type,
                    "format": format_type,
                    "doc_file_path": doc_file_path
                },
                logs=[f"Documentation generated for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Documentation task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Documentation generation failed: {e}"]
            )
    
    async def _handle_general_dev_task(self, task: Task) -> TaskResult:
        """Handle general development tasks."""
        try:
            self.logger.info(f"Handling general dev task: {task.title}")
            
            # Use LLM to process general development task
            dev_prompt = f"""
            As a Senior Developer, handle this development task: {task.title}
            
            Description: {task.description}
            
            Please provide a comprehensive technical solution that addresses the task requirements,
            including any code examples, implementation details, or recommendations.
            """
            
            llm_response = await self.llm_client.generate(
                prompt=dev_prompt,
                system_prompt="You are a senior software developer handling various development tasks."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM processing failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "solution": llm_response.content,
                    "task_type": "general_dev"
                },
                logs=[f"General dev task completed: {task.title}"],
                duration_seconds=1.5
            )
            
        except Exception as e:
            self.logger.error(f"General dev task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"General dev task failed: {e}"]
            )
    
    def _add_to_file_history(self, operation: str, path: str, size: int) -> None:
        """Add file operation to history."""
        self.file_history.append({
            "operation": operation,
            "path": path,
            "size": size,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Keep only last 100 entries
        if len(self.file_history) > 100:
            self.file_history = self.file_history[-100:]
    
    async def get_capabilities(self) -> AgentCapabilities:
        """Get Dev agent capabilities."""
        return self.agent.capabilities
    
    # Public methods for external interaction
    async def generate_code_file(self, file_path: str, description: str, language: str = "python") -> Dict[str, Any]:
        """Generate and save a code file."""
        try:
            # Create code generation task
            code_task = create_task(
                title=f"Generate {file_path}",
                description=description,
                task_type=TaskType.DEVELOPMENT,
                created_by=self.agent.id,
                created_by_role=self.agent.role
            )
            
            # Add context
            code_task.set_context("file_path", file_path)
            code_task.set_context("language", language)
            code_task.set_context("code_type", "file")
            
            # Process the task
            result = await self.process_task(code_task)
            
            return {
                "success": result.success,
                "file_path": file_path,
                "code": result.output.get("generated_code") if result.success else None,
                "error": result.error
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate code file: {e}")
            return {"success": False, "error": str(e)}
    
    def get_file_history(self) -> List[Dict[str, Any]]:
        """Get file operation history."""
        return self.file_history.copy()
    
    def set_project_path(self, path: str) -> None:
        """Set current project path."""
        self.current_project_path = path
        self.logger.info(f"Project path set to: {path}")
    
    def get_project_path(self) -> Optional[str]:
        """Get current project path."""
        return self.current_project_path
