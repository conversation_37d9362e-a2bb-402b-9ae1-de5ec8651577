"""
Custom exception classes and error handling utilities for the Multi-Agent Development System.

This module provides a comprehensive error handling system with custom exceptions,
error codes, and recovery strategies.
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from ..models.enums import AgentRole, TaskStatus, MessageType


class ErrorCode(Enum):
    """Error codes for different types of errors."""
    
    # General errors (1000-1999)
    UNKNOWN_ERROR = "E1000"
    CONFIGURATION_ERROR = "E1001"
    VALIDATION_ERROR = "E1002"
    PERMISSION_ERROR = "E1003"
    RESOURCE_ERROR = "E1004"
    TIMEOUT_ERROR = "E1005"
    
    # Agent errors (2000-2999)
    AGENT_NOT_FOUND = "E2000"
    AGENT_OFFLINE = "E2001"
    AGENT_BUSY = "E2002"
    AGENT_INITIALIZATION_ERROR = "E2003"
    AGENT_COMMUNICATION_ERROR = "E2004"
    AGENT_CAPABILITY_ERROR = "E2005"
    
    # Task errors (3000-3999)
    TASK_NOT_FOUND = "E3000"
    TASK_INVALID_STATUS = "E3001"
    TASK_DEPENDENCY_ERROR = "E3002"
    TASK_EXECUTION_ERROR = "E3003"
    TASK_TIMEOUT_ERROR = "E3004"
    TASK_VALIDATION_ERROR = "E3005"
    
    # Communication errors (4000-4999)
    MESSAGE_DELIVERY_ERROR = "E4000"
    MESSAGE_FORMAT_ERROR = "E4001"
    MESSAGE_TIMEOUT_ERROR = "E4002"
    COMMUNICATION_CHANNEL_ERROR = "E4003"
    BROADCAST_ERROR = "E4004"
    
    # LLM errors (5000-5999)
    LLM_API_ERROR = "E5000"
    LLM_RATE_LIMIT_ERROR = "E5001"
    LLM_AUTHENTICATION_ERROR = "E5002"
    LLM_MODEL_ERROR = "E5003"
    LLM_RESPONSE_ERROR = "E5004"
    
    # Tool errors (6000-6999)
    TOOL_NOT_FOUND = "E6000"
    TOOL_EXECUTION_ERROR = "E6001"
    TOOL_CONFIGURATION_ERROR = "E6002"
    GITHUB_API_ERROR = "E6003"
    CODE_EXECUTION_ERROR = "E6004"
    
    # Database errors (7000-7999)
    DATABASE_CONNECTION_ERROR = "E7000"
    DATABASE_QUERY_ERROR = "E7001"
    DATABASE_TRANSACTION_ERROR = "E7002"
    REDIS_CONNECTION_ERROR = "E7003"
    VECTOR_DB_ERROR = "E7004"
    
    # System errors (8000-8999)
    SYSTEM_OVERLOAD = "E8000"
    MEMORY_ERROR = "E8001"
    DISK_SPACE_ERROR = "E8002"
    NETWORK_ERROR = "E8003"
    SECURITY_ERROR = "E8004"


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MultiAgentException(Exception):
    """Base exception class for the multi-agent system."""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        recoverable: bool = True
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}
        self.cause = cause
        self.recoverable = recoverable
        self.timestamp = datetime.utcnow()
        self.stack_trace = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            'message': self.message,
            'error_code': self.error_code.value,
            'severity': self.severity.value,
            'context': self.context,
            'recoverable': self.recoverable,
            'timestamp': self.timestamp.isoformat(),
            'stack_trace': self.stack_trace,
            'cause': str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        """String representation of the exception."""
        return f"[{self.error_code.value}] {self.message}"


class AgentException(MultiAgentException):
    """Exception related to agent operations."""
    
    def __init__(
        self,
        message: str,
        agent_id: Optional[str] = None,
        agent_role: Optional[AgentRole] = None,
        error_code: ErrorCode = ErrorCode.AGENT_NOT_FOUND,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if agent_id:
            context['agent_id'] = agent_id
        if agent_role:
            context['agent_role'] = agent_role.value
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class TaskException(MultiAgentException):
    """Exception related to task operations."""
    
    def __init__(
        self,
        message: str,
        task_id: Optional[str] = None,
        task_status: Optional[TaskStatus] = None,
        error_code: ErrorCode = ErrorCode.TASK_EXECUTION_ERROR,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if task_id:
            context['task_id'] = task_id
        if task_status:
            context['task_status'] = task_status.value
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class CommunicationException(MultiAgentException):
    """Exception related to communication operations."""
    
    def __init__(
        self,
        message: str,
        sender_id: Optional[str] = None,
        receiver_id: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        error_code: ErrorCode = ErrorCode.MESSAGE_DELIVERY_ERROR,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if sender_id:
            context['sender_id'] = sender_id
        if receiver_id:
            context['receiver_id'] = receiver_id
        if message_type:
            context['message_type'] = message_type.value
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class LLMException(MultiAgentException):
    """Exception related to LLM operations."""
    
    def __init__(
        self,
        message: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.LLM_API_ERROR,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if provider:
            context['provider'] = provider
        if model:
            context['model'] = model
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class ToolException(MultiAgentException):
    """Exception related to tool operations."""
    
    def __init__(
        self,
        message: str,
        tool_name: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.TOOL_EXECUTION_ERROR,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if tool_name:
            context['tool_name'] = tool_name
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class DatabaseException(MultiAgentException):
    """Exception related to database operations."""
    
    def __init__(
        self,
        message: str,
        database_type: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.DATABASE_CONNECTION_ERROR,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if database_type:
            context['database_type'] = database_type
        if operation:
            context['operation'] = operation
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


class SystemException(MultiAgentException):
    """Exception related to system operations."""
    
    def __init__(
        self,
        message: str,
        component: Optional[str] = None,
        error_code: ErrorCode = ErrorCode.SYSTEM_OVERLOAD,
        **kwargs
    ):
        context = kwargs.get('context', {})
        if component:
            context['component'] = component
        kwargs['context'] = context
        super().__init__(message, error_code, **kwargs)


# Error handling utilities
class ErrorHandler:
    """Centralized error handling and recovery."""
    
    def __init__(self):
        self.error_history: List[Dict[str, Any]] = []
        self.recovery_strategies: Dict[ErrorCode, callable] = {}
        self.max_history_size = 1000
    
    def handle_error(
        self,
        error: Union[Exception, MultiAgentException],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Handle an error and attempt recovery."""
        # Convert to MultiAgentException if needed
        if not isinstance(error, MultiAgentException):
            error = MultiAgentException(
                message=str(error),
                cause=error,
                context=context
            )
        
        # Log error
        error_data = error.to_dict()
        if context:
            error_data['context'].update(context)
        
        self._add_to_history(error_data)
        
        # Attempt recovery
        recovery_result = self._attempt_recovery(error)
        error_data['recovery_attempted'] = recovery_result is not None
        error_data['recovery_successful'] = recovery_result.get('success', False) if recovery_result else False
        
        return error_data
    
    def _add_to_history(self, error_data: Dict[str, Any]) -> None:
        """Add error to history."""
        self.error_history.append(error_data)
        
        # Trim history if too large
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
    
    def _attempt_recovery(self, error: MultiAgentException) -> Optional[Dict[str, Any]]:
        """Attempt to recover from an error."""
        if not error.recoverable:
            return None
        
        recovery_strategy = self.recovery_strategies.get(error.error_code)
        if recovery_strategy:
            try:
                result = recovery_strategy(error)
                return {'success': True, 'result': result}
            except Exception as e:
                return {'success': False, 'error': str(e)}
        
        return None
    
    def register_recovery_strategy(self, error_code: ErrorCode, strategy: callable) -> None:
        """Register a recovery strategy for an error code."""
        self.recovery_strategies[error_code] = strategy
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics."""
        if not self.error_history:
            return {'total_errors': 0}
        
        error_counts = {}
        severity_counts = {}
        recent_errors = []
        
        for error in self.error_history:
            # Count by error code
            error_code = error.get('error_code', 'unknown')
            error_counts[error_code] = error_counts.get(error_code, 0) + 1
            
            # Count by severity
            severity = error.get('severity', 'unknown')
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # Recent errors (last 10)
            if len(recent_errors) < 10:
                recent_errors.append({
                    'timestamp': error.get('timestamp'),
                    'error_code': error_code,
                    'message': error.get('message'),
                    'severity': severity
                })
        
        return {
            'total_errors': len(self.error_history),
            'error_counts': error_counts,
            'severity_counts': severity_counts,
            'recent_errors': recent_errors
        }
    
    def clear_history(self) -> None:
        """Clear error history."""
        self.error_history.clear()


# Global error handler instance
error_handler = ErrorHandler()

# Convenience functions
def handle_error(error: Union[Exception, MultiAgentException], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Handle an error using the global error handler."""
    return error_handler.handle_error(error, context)


def register_recovery_strategy(error_code: ErrorCode, strategy: callable) -> None:
    """Register a recovery strategy."""
    error_handler.register_recovery_strategy(error_code, strategy)


def get_error_statistics() -> Dict[str, Any]:
    """Get error statistics."""
    return error_handler.get_error_statistics()


# Decorator for error handling
def handle_exceptions(
    error_type: type = MultiAgentException,
    error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    recoverable: bool = True
):
    """Decorator for automatic exception handling."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if isinstance(e, MultiAgentException):
                    raise e
                else:
                    raise error_type(
                        message=f"Error in {func.__name__}: {str(e)}",
                        error_code=error_code,
                        severity=severity,
                        cause=e,
                        recoverable=recoverable
                    )
        return wrapper
    return decorator


# Context manager for error handling
class ErrorContext:
    """Context manager for error handling."""
    
    def __init__(
        self,
        operation: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None
    ):
        self.operation = operation
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}
    
    def __enter__(self) -> 'ErrorContext':
        """Enter error context."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        """Exit error context and handle any exceptions."""
        if exc_type is not None:
            if not isinstance(exc_val, MultiAgentException):
                exc_val = MultiAgentException(
                    message=f"Error in {self.operation}: {str(exc_val)}",
                    error_code=self.error_code,
                    severity=self.severity,
                    cause=exc_val,
                    context=self.context
                )
            
            handle_error(exc_val, self.context)
            return False  # Re-raise the exception
        
        return True


def error_context(
    operation: str,
    error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Optional[Dict[str, Any]] = None
) -> ErrorContext:
    """Create an error handling context manager."""
    return ErrorContext(operation, error_code, severity, context)
