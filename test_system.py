#!/usr/bin/env python3
"""
System Test Script for Multi-Agent Development System.

This script tests the basic functionality of the multi-agent system
without requiring external dependencies like Redis or LLM APIs.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported successfully."""
    print("🔍 Testing imports...")
    
    try:
        # Test shared modules
        from shared.models import AgentRole, TaskType, MessageType, AgentStatus
        from shared.utils import get_logger, get_config
        print("✅ Shared modules imported successfully")
        
        # Test agent modules
        from agents import BaseAgent, ProjectManagerAgent, DeveloperAgent, TestAgent
        print("✅ Agent modules imported successfully")
        
        # Test communication modules
        from shared.communication import RedisClient, CommunicationHub
        print("✅ Communication modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_models():
    """Test basic model functionality."""
    print("\n🔍 Testing models...")
    
    try:
        from shared.models import (
            Agent, Task, Message, AgentRole, TaskType, MessageType, 
            TaskPriority, TaskStatus, AgentStatus, create_task, create_message
        )
        
        # Test Agent creation
        agent = Agent(
            id="test_agent",
            name="Test Agent",
            role=AgentRole.DEVELOPER,
            status=AgentStatus.IDLE,
            description="Test agent for system validation"
        )
        print(f"✅ Agent created: {agent.name} ({agent.role.value})")
        
        # Test Task creation
        task = create_task(
            title="Test Task",
            description="A test task for validation",
            task_type=TaskType.DEVELOPMENT,
            created_by=agent.id,
            created_by_role=agent.role,
            priority=TaskPriority.MEDIUM
        )
        print(f"✅ Task created: {task.title} (ID: {task.id})")
        
        # Test Message creation
        message = create_message(
            sender_id=agent.id,
            sender_role=agent.role,
            receiver_id="test_receiver",
            receiver_role=AgentRole.PROJECT_MANAGER,
            message_type=MessageType.TASK,
            content="Test message content"
        )
        print(f"✅ Message created: {message.message_type.value} (ID: {message.id})")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_agent_initialization():
    """Test agent initialization without external dependencies."""
    print("\n🔍 Testing agent initialization...")

    try:
        from shared.models import AgentRole, AgentCapabilities, AgentConfiguration, LLMProvider, Agent

        # Test basic agent model creation
        capabilities = AgentCapabilities(
            skills=["test_skill"],
            tools=[],
            languages=["python"],
            frameworks=["test_framework"],
            max_concurrent_tasks=1
        )

        configuration = AgentConfiguration(
            llm_provider=LLMProvider.OPENAI,
            model_name="gpt-4",
            temperature=0.1,
            max_tokens=1000
        )

        # Create a simple agent model
        agent = Agent(
            name="Test Agent",
            role=AgentRole.DEVELOPER,
            description="Test agent for validation",
            capabilities=capabilities,
            configuration=configuration
        )

        print(f"✅ Agent model created: {agent.name}")
        print(f"   - Role: {agent.role}")
        print(f"   - Skills: {len(agent.capabilities.skills)} skills")
        print(f"   - ID: {agent.id}")

        return True

    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_task_processing():
    """Test basic task processing without LLM calls."""
    print("\n🔍 Testing basic task processing...")

    try:
        from shared.models import create_task, TaskType, AgentRole

        # Create a simple task
        file_task = create_task(
            title="Create test file",
            description="Create a simple test file",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_system",
            created_by_role=AgentRole.COORDINATOR
        )

        # Add context for file operation
        file_task.set_context("operation", "create")
        file_task.set_context("path", "test_output.txt")
        file_task.set_context("content", "# Test file created by system test\nThis is a test file.")

        print(f"✅ Task created: {file_task.title}")
        print(f"   - Type: {file_task.task_type}")
        print(f"   - Context: {len(file_task.metadata.context)} items")
        print(f"   - ID: {file_task.id}")

        # Test task serialization
        task_dict = file_task.model_dump()
        print(f"✅ Task serialization works: {len(task_dict)} fields")

        return True

    except Exception as e:
        print(f"❌ Task processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration system."""
    print("\n🔍 Testing configuration...")
    
    try:
        from shared.utils import get_config, get_logger
        
        # Test config with defaults
        test_value = get_config("test_key", "default_value")
        print(f"✅ Config system working: test_key = {test_value}")
        
        # Test logger
        logger = get_logger("test_logger")
        logger.info("Test log message")
        print("✅ Logger system working")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_project_structure():
    """Test that all required directories and files exist."""
    print("\n🔍 Testing project structure...")
    
    required_dirs = [
        "agents",
        "shared",
        "shared/models",
        "shared/utils", 
        "shared/llm",
        "shared/tools",
        "shared/communication",
        "tests",
        "config",
        "dashboard"
    ]
    
    required_files = [
        "pyproject.toml",
        "requirements.txt",
        "agents/__init__.py",
        "shared/__init__.py",
        "shared/models/__init__.py",
        "shared/utils/__init__.py"
    ]
    
    missing_dirs = []
    missing_files = []
    
    # Check directories
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    # Check files
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required directories and files exist")
    return True

async def main():
    """Run all system tests."""
    print("🚀 Multi-Agent Development System - System Test")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Imports", test_imports),
        ("Models", test_models),
        ("Configuration", test_configuration),
        ("Agent Initialization", test_agent_initialization),
        ("Basic Task Processing", test_basic_task_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
