"""
Tools module for the Multi-Agent Development System.

This module provides a comprehensive tool management system that allows agents
to discover, load, and execute various tools for different tasks including
code generation, file management, GitHub operations, and more.
"""

from .manager import (
    ToolManager,
    BaseTool,
    ToolResult,
    ToolInfo,
    CodeGeneratorTool,
    FileManagerTool,
)

from .github_client import GitHubClientTool

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"

# Export all public classes
__all__ = [
    "ToolManager",
    "BaseTool",
    "ToolResult",
    "ToolInfo",
    "CodeGeneratorTool",
    "FileManagerTool",
    "GitHubClientTool",
]