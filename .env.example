# =============================================================================
# Multi-Agent Development System - Environment Configuration
# =============================================================================

# =============================================================================
# LLM Provider Configuration
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here
OPENAI_DEFAULT_MODEL=gpt-4-turbo

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229

# Ollama Configuration (for local models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=llama2

# LLM Provider Priority (comma-separated)
LLM_PROVIDER_PRIORITY=openai,anthropic,ollama

# =============================================================================
# Database Configuration
# =============================================================================

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# PostgreSQL Configuration (optional)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=multi_agent_system
POSTGRES_USER=agent_user
POSTGRES_PASSWORD=agent_password
DATABASE_URL=postgresql://agent_user:agent_password@localhost:5432/multi_agent_system

# Vector Database Configuration
CHROMA_PERSIST_DIRECTORY=./data/chroma
CHROMA_COLLECTION_NAME=agent_memory

# =============================================================================
# GitHub Integration
# =============================================================================

# GitHub API Configuration
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_USERNAME=your_github_username
GITHUB_REPO_OWNER=your_github_username
GITHUB_REPO_NAME=multi-agent-projects

# =============================================================================
# Agent Configuration
# =============================================================================

# Agent Behavior Settings
AGENT_MAX_RETRIES=3
AGENT_TIMEOUT_SECONDS=30
AGENT_MEMORY_LIMIT=1000
AGENT_LOG_LEVEL=INFO

# PM Agent Configuration
PM_AGENT_MODEL=gpt-4-turbo
PM_AGENT_TEMPERATURE=0.3
PM_AGENT_MAX_TOKENS=2000

# Dev Agent Configuration
DEV_AGENT_MODEL=claude-3-sonnet-20240229
DEV_AGENT_TEMPERATURE=0.1
DEV_AGENT_MAX_TOKENS=4000

# Test Agent Configuration
TEST_AGENT_MODEL=gpt-4
TEST_AGENT_TEMPERATURE=0.2
TEST_AGENT_MAX_TOKENS=1500

# =============================================================================
# Communication Configuration
# =============================================================================

# WebSocket Configuration
WEBSOCKET_HOST=localhost
WEBSOCKET_PORT=8765
WEBSOCKET_PATH=/ws

# Message Queue Configuration
MESSAGE_QUEUE_MAX_SIZE=1000
MESSAGE_RETENTION_HOURS=24

# =============================================================================
# Web Dashboard Configuration
# =============================================================================

# FastAPI Configuration
API_HOST=localhost
API_PORT=8080
API_RELOAD=true
API_DEBUG=true

# Dashboard Configuration
DASHBOARD_TITLE=Multi-Agent Development System
DASHBOARD_VERSION=1.0.0
DASHBOARD_SECRET_KEY=your_secret_key_here

# =============================================================================
# Security Configuration
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# =============================================================================
# Monitoring & Logging
# =============================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=./logs/agents.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30
HEALTH_CHECK_TIMEOUT_SECONDS=5

# =============================================================================
# Development Configuration
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true
TESTING=false

# Code Execution
CODE_EXECUTION_TIMEOUT=60
CODE_EXECUTION_MEMORY_LIMIT=512
SANDBOX_ENABLED=true

# Docker Configuration
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_NETWORK=multi-agent-network

# =============================================================================
# Performance Configuration
# =============================================================================

# Concurrency Settings
MAX_CONCURRENT_AGENTS=10
MAX_CONCURRENT_TASKS=50
WORKER_POOL_SIZE=4

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# Feature Flags
# =============================================================================

# Feature Toggles
ENABLE_VECTOR_MEMORY=true
ENABLE_GITHUB_INTEGRATION=true
ENABLE_CODE_EXECUTION=true
ENABLE_METRICS=true
ENABLE_WEB_DASHBOARD=true

# Experimental Features
ENABLE_MULTI_MODAL=false
ENABLE_VOICE_INTERFACE=false
ENABLE_ADVANCED_PLANNING=false

# =============================================================================
# External Services
# =============================================================================

# Optional: Pinecone Configuration
# PINECONE_API_KEY=your_pinecone_api_key
# PINECONE_ENVIRONMENT=your_pinecone_environment
# PINECONE_INDEX_NAME=agent-memory

# Optional: Weaviate Configuration
# WEAVIATE_URL=http://localhost:8080
# WEAVIATE_API_KEY=your_weaviate_api_key

# Optional: Elasticsearch Configuration
# ELASTICSEARCH_URL=http://localhost:9200
# ELASTICSEARCH_USERNAME=elastic
# ELASTICSEARCH_PASSWORD=your_password

# =============================================================================
# Backup & Recovery
# =============================================================================

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=6
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# =============================================================================
# Notification Configuration
# =============================================================================

# Email Notifications (optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# NOTIFICATION_EMAIL=<EMAIL>

# Slack Notifications (optional)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
# SLACK_CHANNEL=#multi-agent-alerts
