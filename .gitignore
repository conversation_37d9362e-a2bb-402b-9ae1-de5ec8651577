# =============================================================================
# Multi-Agent Development System - Git Ignore
# =============================================================================

# =============================================================================
# Python
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# IDEs and Editors
# =============================================================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Project Specific
# =============================================================================

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Configuration files with secrets
config/secrets.yaml
config/production.yaml
config/local.yaml

# Logs
logs/
*.log
*.log.*
agent_*.log
system_*.log

# Data directories
data/
storage/
uploads/
downloads/
temp/
tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# Vector database storage
chroma_db/
vector_store/
embeddings/

# Redis dumps
dump.rdb
appendonly.aof

# Backup files
backups/
*.backup
*.bak

# Generated files
generated/
output/
results/

# Cache directories
.cache/
cache/
.redis_cache/

# =============================================================================
# Docker
# =============================================================================
.dockerignore
docker-compose.override.yml
docker-compose.local.yml
.docker/

# =============================================================================
# AI/ML Specific
# =============================================================================

# Model files
models/
*.model
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.onnx
*.pb
*.pth
*.pt

# Datasets
datasets/
*.csv
*.json
*.jsonl
*.parquet

# Jupyter notebooks checkpoints
.ipynb_checkpoints/

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# =============================================================================
# Monitoring & Metrics
# =============================================================================

# Prometheus data
prometheus_data/

# Grafana data
grafana_data/

# Metrics files
metrics/
*.metrics

# =============================================================================
# Development Tools
# =============================================================================

# Pre-commit
.pre-commit-config.yaml.bak

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling
*.prof
*.profile

# Benchmarks
benchmarks/results/

# =============================================================================
# Deployment
# =============================================================================

# Kubernetes
k8s/secrets/
*.secret.yaml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# =============================================================================
# Documentation
# =============================================================================

# Generated docs
docs/build/
docs/_build/
site/

# API documentation
api_docs/generated/

# =============================================================================
# Testing
# =============================================================================

# Test results
test-results/
test_results/
.pytest_cache/
.coverage
htmlcov/

# Test data
test_data/
fixtures/

# =============================================================================
# Security
# =============================================================================

# API keys and secrets
*.key
*.pem
*.crt
*.p12
secrets.json
credentials.json

# =============================================================================
# Temporary Files
# =============================================================================

# Temporary directories
tmp/
temp/
.tmp/

# Lock files
*.lock
.lock

# PID files
*.pid

# Socket files
*.sock

# =============================================================================
# Custom Project Ignores
# =============================================================================

# Agent-specific data
agents/*/data/
agents/*/logs/
agents/*/cache/

# Communication logs
communication_logs/
message_history/

# Generated code
generated_code/
auto_generated/

# Experimental features
experiments/
sandbox/

# Performance data
performance_logs/
profiling_data/

# User-specific configurations
user_config.yaml
local_settings.py
