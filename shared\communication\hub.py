"""
Communication Hub for the Multi-Agent Development System.

This module provides the central communication hub that manages message routing,
agent registration, and inter-agent communication.
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Set, Callable, Any
from datetime import datetime
from collections import defaultdict

from ..models import (
    Message, MessageType, AgentRole, AgentStatus,
    BroadcastMessage, create_notification_message
)
from ..utils import (
    get_logger, handle_error, CommunicationException, ErrorCode,
    log_communication_event, log_performance
)
from .redis_client import RedisClient


class CommunicationHub:
    """Central hub for agent communication."""
    
    def __init__(self, redis_client: Optional[RedisClient] = None):
        """Initialize communication hub."""
        self.logger = get_logger("communication_hub")
        
        # Redis client for persistent messaging
        self.redis_client = redis_client or RedisClient()
        
        # Registered agents
        self.agents: Dict[str, Dict[str, Any]] = {}  # agent_id -> agent_info
        self.agent_roles: Dict[AgentR<PERSON>, Set[str]] = defaultdict(set)  # role -> agent_ids
        
        # Message routing
        self.message_handlers: Dict[str, Callable] = {}  # agent_id -> handler
        self.broadcast_subscribers: Set[str] = set()
        
        # Message history and statistics
        self.message_history: List[Message] = []
        self.message_stats: Dict[str, int] = defaultdict(int)
        self.max_history_size = 1000
        
        # Hub status
        self.running = False
        self.start_time: Optional[datetime] = None
        
        self.logger.info("Communication hub initialized")
    
    async def start(self) -> None:
        """Start the communication hub."""
        try:
            self.logger.info("Starting communication hub")
            
            # Connect to Redis
            await self.redis_client.connect()
            
            # Subscribe to broadcast channel
            await self.redis_client.subscribe_to_channel(
                "agent_broadcast",
                self._handle_broadcast_message
            )
            
            # Start message processing
            self.running = True
            self.start_time = datetime.utcnow()
            
            # Start background tasks
            asyncio.create_task(self._message_processor())
            asyncio.create_task(self._status_updater())
            
            log_communication_event(
                "hub_started",
                details={"timestamp": self.start_time.isoformat()}
            )
            
            self.logger.info("Communication hub started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start communication hub: {e}")
            raise CommunicationException(
                f"Hub startup failed: {str(e)}",
                error_code=ErrorCode.COMMUNICATION_CHANNEL_ERROR
            )
    
    async def stop(self) -> None:
        """Stop the communication hub."""
        try:
            self.logger.info("Stopping communication hub")
            
            self.running = False
            
            # Disconnect from Redis
            await self.redis_client.disconnect()
            
            # Clear registrations
            self.agents.clear()
            self.agent_roles.clear()
            self.message_handlers.clear()
            
            log_communication_event(
                "hub_stopped",
                details={"uptime_seconds": self._get_uptime_seconds()}
            )
            
            self.logger.info("Communication hub stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping communication hub: {e}")
    
    async def register_agent(
        self,
        agent_id: str,
        agent_role: AgentRole,
        agent_name: str,
        message_handler: Optional[Callable] = None
    ) -> bool:
        """Register an agent with the hub."""
        try:
            agent_info = {
                "id": agent_id,
                "name": agent_name,
                "role": agent_role,
                "status": AgentStatus.OFFLINE,
                "registered_at": datetime.utcnow(),
                "last_seen": datetime.utcnow(),
                "message_count": 0
            }
            
            self.agents[agent_id] = agent_info
            self.agent_roles[agent_role].add(agent_id)
            
            if message_handler:
                self.message_handlers[agent_id] = message_handler
            
            # Update Redis
            await self.redis_client.set_agent_status(agent_id, agent_info)
            
            # Notify other agents
            notification = create_notification_message(
                sender_id="communication_hub",
                sender_role=AgentRole.COORDINATOR,
                notification_type="agent_registered",
                content=f"Agent {agent_name} ({agent_role.value}) has joined the system"
            )
            await self._broadcast_message(notification, exclude_agents=[agent_id])
            
            log_communication_event(
                "agent_registered",
                details={
                    "agent_id": agent_id,
                    "agent_name": agent_name,
                    "agent_role": agent_role.value
                }
            )
            
            self.logger.info(f"Agent registered: {agent_name} ({agent_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent_id}: {e}")
            handle_error(e, {"operation": "register_agent", "agent_id": agent_id})
            return False
    
    async def unregister_agent(self, agent_id: str) -> bool:
        """Unregister an agent from the hub."""
        try:
            if agent_id not in self.agents:
                return False
            
            agent_info = self.agents[agent_id]
            agent_role = agent_info["role"]
            agent_name = agent_info["name"]
            
            # Remove from collections
            del self.agents[agent_id]
            self.agent_roles[agent_role].discard(agent_id)
            self.message_handlers.pop(agent_id, None)
            
            # Notify other agents
            notification = create_notification_message(
                sender_id="communication_hub",
                sender_role=AgentRole.COORDINATOR,
                notification_type="agent_unregistered",
                content=f"Agent {agent_name} ({agent_role.value}) has left the system"
            )
            await self._broadcast_message(notification, exclude_agents=[agent_id])
            
            log_communication_event(
                "agent_unregistered",
                details={
                    "agent_id": agent_id,
                    "agent_name": agent_name,
                    "agent_role": agent_role.value
                }
            )
            
            self.logger.info(f"Agent unregistered: {agent_name} ({agent_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unregister agent {agent_id}: {e}")
            return False
    
    async def send_message(self, message: Message) -> bool:
        """Send a message through the hub."""
        try:
            with log_performance("hub_send_message", self.logger):
                # Add to history
                self._add_to_history(message)
                
                # Update statistics
                self.message_stats["total_sent"] += 1
                self.message_stats[f"type_{message.message_type.value}"] += 1
                
                # Route message
                if message.is_broadcast():
                    return await self._handle_broadcast(message)
                else:
                    return await self._handle_direct_message(message)
                    
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            handle_error(e, {
                "operation": "send_message",
                "message_id": message.id,
                "message_type": message.message_type.value
            })
            return False
    
    async def _handle_direct_message(self, message: Message) -> bool:
        """Handle direct message routing."""
        try:
            receiver_id = message.receiver_id
            
            if not receiver_id:
                self.logger.error("Direct message missing receiver_id")
                return False
            
            if receiver_id not in self.agents:
                self.logger.warning(f"Receiver not found: {receiver_id}")
                return False
            
            # Update agent last seen
            self.agents[receiver_id]["last_seen"] = datetime.utcnow()
            
            # Try direct handler first
            if receiver_id in self.message_handlers:
                handler = self.message_handlers[receiver_id]
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(message)
                    else:
                        handler(message)
                    
                    log_communication_event(
                        "message_delivered",
                        sender_id=message.sender_id,
                        receiver_id=receiver_id,
                        message_type=message.message_type.value
                    )
                    return True
                    
                except Exception as e:
                    self.logger.error(f"Message handler failed for {receiver_id}: {e}")
            
            # Fallback to Redis queue
            queue_key = f"agent_queue:{receiver_id}"
            return await self.redis_client.send_message(message, queue_key)
            
        except Exception as e:
            self.logger.error(f"Failed to handle direct message: {e}")
            return False
    
    async def _handle_broadcast(self, message: Message) -> bool:
        """Handle broadcast message routing."""
        try:
            if isinstance(message, BroadcastMessage):
                # Use BroadcastMessage logic
                target_agents = []
                for agent_id, agent_info in self.agents.items():
                    if message.should_receive(agent_id, agent_info["role"]):
                        target_agents.append(agent_id)
            else:
                # Broadcast to all agents except sender
                target_agents = [
                    agent_id for agent_id in self.agents.keys()
                    if agent_id != message.sender_id
                ]
            
            success_count = 0
            for agent_id in target_agents:
                # Create individual message for each agent
                direct_message = Message(
                    sender_id=message.sender_id,
                    sender_role=message.sender_role,
                    receiver_id=agent_id,
                    receiver_role=self.agents[agent_id]["role"],
                    message_type=message.message_type,
                    content=message.content,
                    metadata=message.metadata
                )
                
                if await self._handle_direct_message(direct_message):
                    success_count += 1
            
            # Also publish to Redis broadcast channel
            await self.redis_client.publish_message("agent_broadcast", message)
            
            log_communication_event(
                "broadcast_sent",
                sender_id=message.sender_id,
                details={
                    "target_count": len(target_agents),
                    "success_count": success_count
                }
            )
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"Failed to handle broadcast: {e}")
            return False
    
    async def _broadcast_message(self, message: Message, exclude_agents: Optional[List[str]] = None) -> bool:
        """Internal broadcast helper."""
        exclude_agents = exclude_agents or []
        
        broadcast_msg = BroadcastMessage(
            sender_id=message.sender_id,
            sender_role=message.sender_role,
            message_type=message.message_type,
            content=message.content,
            exclude_agents=exclude_agents
        )
        
        return await self._handle_broadcast(broadcast_msg)
    
    async def _handle_broadcast_message(self, message: Message) -> None:
        """Handle incoming broadcast message from Redis."""
        try:
            # Route to local agents
            await self._handle_broadcast(message)
            
        except Exception as e:
            self.logger.error(f"Failed to handle broadcast message: {e}")
    
    async def _message_processor(self) -> None:
        """Background message processor."""
        while self.running:
            try:
                # Process messages from Redis queues
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                
                # Listen for Redis pub/sub messages
                if self.redis_client.pubsub:
                    await self.redis_client.listen_for_messages()
                    
            except Exception as e:
                self.logger.error(f"Error in message processor: {e}")
                await asyncio.sleep(1)  # Longer delay on error
    
    async def _status_updater(self) -> None:
        """Background status updater."""
        while self.running:
            try:
                # Update agent statuses in Redis
                for agent_id, agent_info in self.agents.items():
                    await self.redis_client.set_agent_status(agent_id, agent_info)
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in status updater: {e}")
                await asyncio.sleep(60)  # Longer delay on error
    
    def _add_to_history(self, message: Message) -> None:
        """Add message to history."""
        self.message_history.append(message)
        
        # Trim history if too large
        if len(self.message_history) > self.max_history_size:
            self.message_history = self.message_history[-self.max_history_size:]
    
    def _get_uptime_seconds(self) -> float:
        """Get hub uptime in seconds."""
        if self.start_time:
            return (datetime.utcnow() - self.start_time).total_seconds()
        return 0.0
    
    # Query methods
    def get_registered_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get all registered agents."""
        return self.agents.copy()
    
    def get_agents_by_role(self, role: AgentRole) -> List[str]:
        """Get agent IDs by role."""
        return list(self.agent_roles[role])
    
    def is_agent_registered(self, agent_id: str) -> bool:
        """Check if agent is registered."""
        return agent_id in self.agents
    
    def get_message_history(self, limit: Optional[int] = None) -> List[Message]:
        """Get message history."""
        if limit:
            return self.message_history[-limit:]
        return self.message_history.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get hub statistics."""
        return {
            "registered_agents": len(self.agents),
            "agents_by_role": {
                role.value: len(agents) 
                for role, agents in self.agent_roles.items()
            },
            "message_stats": dict(self.message_stats),
            "message_history_size": len(self.message_history),
            "uptime_seconds": self._get_uptime_seconds(),
            "running": self.running
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            redis_health = await self.redis_client.health_check()
            
            return {
                "status": "healthy" if self.running else "stopped",
                "running": self.running,
                "registered_agents": len(self.agents),
                "uptime_seconds": self._get_uptime_seconds(),
                "redis_status": redis_health["status"],
                "message_stats": dict(self.message_stats),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
