"""
Utility modules for the Multi-Agent Development System.

This package provides common utilities including logging, configuration management,
error handling, and helper functions used throughout the system.
"""

# Import logging utilities
from .logger import (
    <PERSON>gger<PERSON>anager,
    AgentLoggerAdapter,
    JSONFormatter,
    ColoredFormatter,
    PerformanceLogger,
    logger_manager,
    get_logger,
    get_agent_logger,
    get_system_logger,
    get_communication_logger,
    get_task_logger,
    set_log_level,
    log_agent_action,
    log_task_event,
    log_communication_event,
    log_system_event,
    log_performance,
)

# Import exception handling
from .exceptions import (
    ErrorCode,
    ErrorSeverity,
    MultiAgentException,
    AgentException,
    TaskException,
    CommunicationException,
    LLMException,
    ToolException,
    DatabaseException,
    SystemException,
    ErrorHandler,
    ErrorContext,
    error_handler,
    handle_error,
    register_recovery_strategy,
    get_error_statistics,
    handle_exceptions,
    error_context,
)

# Import configuration management
from .config import (
    SystemConfig,
    LLMConfig,
    AgentConfig,
    ConfigManager,
    config,
    get_config,
    set_config,
    get_agent_config,
    get_database_url,
    is_feature_enabled,
    validate_config,
)

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"

# Export all public classes and functions
__all__ = [
    # Logging
    "LoggerManager",
    "AgentLoggerAdapter",
    "JSONFormatter",
    "ColoredFormatter",
    "PerformanceLogger",
    "logger_manager",
    "get_logger",
    "get_agent_logger",
    "get_system_logger",
    "get_communication_logger",
    "get_task_logger",
    "set_log_level",
    "log_agent_action",
    "log_task_event",
    "log_communication_event",
    "log_system_event",
    "log_performance",

    # Exception handling
    "ErrorCode",
    "ErrorSeverity",
    "MultiAgentException",
    "AgentException",
    "TaskException",
    "CommunicationException",
    "LLMException",
    "ToolException",
    "DatabaseException",
    "SystemException",
    "ErrorHandler",
    "ErrorContext",
    "error_handler",
    "handle_error",
    "register_recovery_strategy",
    "get_error_statistics",
    "handle_exceptions",
    "error_context",

    # Configuration
    "SystemConfig",
    "LLMConfig",
    "AgentConfig",
    "ConfigManager",
    "config",
    "get_config",
    "set_config",
    "get_agent_config",
    "get_database_url",
    "is_feature_enabled",
    "validate_config",
]