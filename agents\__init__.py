"""
Agents module for the Multi-Agent Development System.

This module provides all agent implementations including the base agent class
and specialized agents for different roles in the development process.
"""

from .base_agent import BaseAgent
from .pm_agent import ProjectManagerAgent
from .dev_agent import DeveloperAgent
from .test_agent import TestAgent

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"

# Export all public classes
__all__ = [
    "BaseAgent",
    "ProjectManagerAgent",
    "DeveloperAgent",
    "TestAgent",
]