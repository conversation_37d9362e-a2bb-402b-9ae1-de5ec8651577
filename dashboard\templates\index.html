{% extends "base.html" %}

{% block title %}Dashboard - Multi-Agent System{% endblock %}

{% block content %}
<div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-tachometer-alt me-2 text-primary"></i>
            System Dashboard
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
            <button class="btn btn-primary btn-sm" onclick="sendTestMessage()">
                <i class="fas fa-paper-plane me-1"></i>Send Test Message
            </button>
        </div>
    </div>
    
    <!-- System Metrics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="metric-value" id="total-agents">{{ stats.registered_agents or 0 }}</div>
                    <div class="metric-label">Registered Agents</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="metric-value" id="total-messages">{{ stats.message_stats.total_sent or 0 }}</div>
                    <div class="metric-label">Messages Sent</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="metric-value" id="uptime">{{ "%.1f"|format(stats.uptime_seconds or 0) }}s</div>
                    <div class="metric-label">System Uptime</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card metric-card h-100">
                <div class="card-body text-center">
                    <div class="metric-value text-success" id="system-status">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="metric-label">System Status</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Agents Overview -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Active Agents
                    </h5>
                </div>
                <div class="card-body">
                    <div id="agents-container">
                        {% if agents %}
                            <div class="row">
                                {% for agent_id, agent in agents.items() %}
                                <div class="col-md-6 mb-3">
                                    <div class="card agent-card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ agent.name }}</h6>
                                                <span class="status-badge status-{{ agent.status.lower() }}">
                                                    {{ agent.status }}
                                                </span>
                                            </div>
                                            <p class="card-text text-muted small mb-2">
                                                <i class="fas fa-tag me-1"></i>{{ agent.role.title() }}
                                            </p>
                                            <div class="small text-muted">
                                                <div><i class="fas fa-clock me-1"></i>Last seen: {{ agent.last_seen }}</div>
                                                <div><i class="fas fa-envelope me-1"></i>Messages: {{ agent.message_count or 0 }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-robot fa-3x mb-3 opacity-50"></i>
                                <p>No agents registered yet</p>
                                <small>Agents will appear here when they connect to the system</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Agent Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div id="agent-roles-chart">
                        {% if stats.agents_by_role %}
                            {% for role, count in stats.agents_by_role.items() %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-capitalize">{{ role.replace('_', ' ') }}</span>
                                <span class="badge bg-primary">{{ count }}</span>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center text-muted py-3">
                                <i class="fas fa-chart-pie fa-2x mb-2 opacity-50"></i>
                                <p class="mb-0">No data available</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recent-activity">
                        <div class="text-center text-muted py-4">
                            <div class="loading me-2"></div>
                            Loading recent activity...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard-specific JavaScript
    
    // Override base functions
    function updateStatistics(stats) {
        document.getElementById('total-agents').textContent = stats.registered_agents || 0;
        document.getElementById('total-messages').textContent = stats.message_stats?.total_sent || 0;
        document.getElementById('uptime').textContent = formatDuration(stats.uptime_seconds || 0);
        
        // Update agent roles chart
        updateAgentRolesChart(stats.agents_by_role || {});
    }
    
    function updateAgents(agents) {
        const container = document.getElementById('agents-container');
        
        if (Object.keys(agents).length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-robot fa-3x mb-3 opacity-50"></i>
                    <p>No agents registered yet</p>
                    <small>Agents will appear here when they connect to the system</small>
                </div>
            `;
            return;
        }
        
        let html = '<div class="row">';
        for (const [agentId, agent] of Object.entries(agents)) {
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card agent-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">${agent.name}</h6>
                                ${getStatusBadge(agent.status)}
                            </div>
                            <p class="card-text text-muted small mb-2">
                                <i class="fas fa-tag me-1"></i>${agent.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </p>
                            <div class="small text-muted">
                                <div><i class="fas fa-clock me-1"></i>Last seen: ${formatTimestamp(agent.last_seen)}</div>
                                <div><i class="fas fa-envelope me-1"></i>Messages: ${agent.message_count || 0}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        html += '</div>';
        
        container.innerHTML = html;
    }
    
    function updateAgentRolesChart(agentsByRole) {
        const container = document.getElementById('agent-roles-chart');
        
        if (Object.keys(agentsByRole).length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-chart-pie fa-2x mb-2 opacity-50"></i>
                    <p class="mb-0">No data available</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        for (const [role, count] of Object.entries(agentsByRole)) {
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-capitalize">${role.replace('_', ' ')}</span>
                    <span class="badge bg-primary">${count}</span>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    // Load recent activity
    async function loadRecentActivity() {
        try {
            const response = await fetch('/api/messages?limit=10');
            const data = await response.json();
            
            const container = document.getElementById('recent-activity');
            
            if (data.messages.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-comments fa-2x mb-2 opacity-50"></i>
                        <p class="mb-0">No recent activity</p>
                    </div>
                `;
                return;
            }
            
            let html = '';
            data.messages.slice(0, 5).forEach(message => {
                html += `
                    <div class="message-item type-${message.message_type}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${message.sender_role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</strong>
                                <span class="text-muted">→</span>
                                <span>${message.receiver_id || 'Broadcast'}</span>
                            </div>
                            <small class="text-muted">${formatTimestamp(message.timestamp)}</small>
                        </div>
                        <div class="mt-1">
                            <span class="badge bg-secondary me-2">${message.message_type}</span>
                            ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
        } catch (error) {
            console.error('Error loading recent activity:', error);
            document.getElementById('recent-activity').innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p class="mb-0">Error loading recent activity</p>
                </div>
            `;
        }
    }
    
    // Refresh all data
    async function refreshData() {
        try {
            // Refresh statistics
            const statsResponse = await fetch('/api/statistics');
            const statsData = await statsResponse.json();
            updateStatistics(statsData.statistics);
            
            // Refresh agents
            const agentsResponse = await fetch('/api/agents');
            const agentsData = await agentsResponse.json();
            updateAgents(agentsData.agents);
            
            // Refresh recent activity
            await loadRecentActivity();
            
        } catch (error) {
            console.error('Error refreshing data:', error);
        }
    }
    
    // Send test message
    async function sendTestMessage() {
        try {
            const response = await fetch('/api/messages/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_type: 'notification',
                    content: 'Test message from dashboard at ' + new Date().toLocaleString(),
                    receiver_id: null
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alert.style.top = '100px';
                alert.style.right = '20px';
                alert.style.zIndex = '1050';
                alert.innerHTML = `
                    <i class="fas fa-check me-2"></i>Test message sent successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alert);
                
                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 3000);
                
                // Refresh recent activity
                setTimeout(loadRecentActivity, 1000);
            }
            
        } catch (error) {
            console.error('Error sending test message:', error);
        }
    }
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        loadRecentActivity();
        
        // Auto-refresh every 30 seconds
        setInterval(refreshData, 30000);
    });
</script>
{% endblock %}
