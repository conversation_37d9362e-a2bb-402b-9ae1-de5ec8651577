"""
GitHub Client Tool for the Multi-Agent Development System.

This module provides GitHub integration capabilities for agents to interact
with repositories, create commits, pull requests, and manage issues.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from github import Github, GithubException
from github.Repository import Repository
from github.ContentFile import ContentFile

from .manager import BaseTool, ToolResult, ToolInfo
from ..models.enums import ToolType, AgentRole
from ..utils import get_config, handle_error, ToolException, ErrorCode


class GitHubClientTool(BaseTool):
    """GitHub client tool for repository operations."""
    
    def __init__(self):
        super().__init__(
            name="github_client",
            tool_type=ToolType.GITHUB_CLIENT,
            description="Interact with GitHub repositories"
        )
        
        # Initialize GitHub client
        self.token = get_config("github_token")
        self.username = get_config("github_username")
        self.repo_owner = get_config("github_repo_owner", self.username)
        self.repo_name = get_config("github_repo_name", "multi-agent-projects")
        
        self.github = None
        self.repo = None
        
        if self.token:
            self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize GitHub client."""
        try:
            self.github = Github(self.token)
            
            # Get or create repository
            try:
                self.repo = self.github.get_repo(f"{self.repo_owner}/{self.repo_name}")
                self.logger.info(f"Connected to repository: {self.repo_owner}/{self.repo_name}")
            except GithubException as e:
                if e.status == 404:
                    self.logger.warning(f"Repository {self.repo_owner}/{self.repo_name} not found")
                    self.repo = None
                else:
                    raise
                    
        except Exception as e:
            self.logger.error(f"Failed to initialize GitHub client: {e}")
            raise ToolException(
                f"GitHub client initialization failed: {str(e)}",
                tool_name=self.name,
                error_code=ErrorCode.GITHUB_API_ERROR
            )
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute GitHub operation."""
        try:
            if not self.github:
                return ToolResult(
                    success=False,
                    output=None,
                    error="GitHub client not initialized. Check token configuration."
                )
            
            operation = kwargs.get("operation", "")
            
            if operation == "create_file":
                return await self._create_file(**kwargs)
            elif operation == "update_file":
                return await self._update_file(**kwargs)
            elif operation == "get_file":
                return await self._get_file(**kwargs)
            elif operation == "list_files":
                return await self._list_files(**kwargs)
            elif operation == "create_branch":
                return await self._create_branch(**kwargs)
            elif operation == "create_pr":
                return await self._create_pull_request(**kwargs)
            elif operation == "create_issue":
                return await self._create_issue(**kwargs)
            elif operation == "commit_multiple":
                return await self._commit_multiple_files(**kwargs)
            else:
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Unknown operation: {operation}"
                )
                
        except Exception as e:
            self.logger.error(f"GitHub operation failed: {e}")
            return ToolResult(
                success=False,
                output=None,
                error=str(e)
            )
    
    async def _create_file(self, **kwargs) -> ToolResult:
        """Create a new file in the repository."""
        try:
            if not self.repo:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Repository not available"
                )
            
            path = kwargs.get("path", "")
            content = kwargs.get("content", "")
            message = kwargs.get("message", f"Create {path}")
            branch = kwargs.get("branch", "main")
            
            if not path or not content:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Path and content are required"
                )
            
            # Create file
            result = self.repo.create_file(
                path=path,
                message=message,
                content=content,
                branch=branch
            )
            
            return ToolResult(
                success=True,
                output={
                    "path": path,
                    "sha": result["commit"].sha,
                    "url": result["content"].html_url
                },
                metadata={
                    "operation": "create_file",
                    "branch": branch,
                    "commit_sha": result["commit"].sha
                }
            )
            
        except GithubException as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"GitHub API error: {e.data.get('message', str(e))}"
            )
    
    async def _update_file(self, **kwargs) -> ToolResult:
        """Update an existing file in the repository."""
        try:
            if not self.repo:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Repository not available"
                )
            
            path = kwargs.get("path", "")
            content = kwargs.get("content", "")
            message = kwargs.get("message", f"Update {path}")
            branch = kwargs.get("branch", "main")
            
            if not path or not content:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Path and content are required"
                )
            
            # Get current file to get SHA
            try:
                file = self.repo.get_contents(path, ref=branch)
                sha = file.sha
            except GithubException as e:
                if e.status == 404:
                    return ToolResult(
                        success=False,
                        output=None,
                        error=f"File not found: {path}"
                    )
                raise
            
            # Update file
            result = self.repo.update_file(
                path=path,
                message=message,
                content=content,
                sha=sha,
                branch=branch
            )
            
            return ToolResult(
                success=True,
                output={
                    "path": path,
                    "sha": result["commit"].sha,
                    "url": result["content"].html_url
                },
                metadata={
                    "operation": "update_file",
                    "branch": branch,
                    "commit_sha": result["commit"].sha
                }
            )
            
        except GithubException as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"GitHub API error: {e.data.get('message', str(e))}"
            )
    
    async def _get_file(self, **kwargs) -> ToolResult:
        """Get file content from repository."""
        try:
            if not self.repo:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Repository not available"
                )
            
            path = kwargs.get("path", "")
            branch = kwargs.get("branch", "main")
            
            if not path:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Path is required"
                )
            
            # Get file
            file = self.repo.get_contents(path, ref=branch)
            
            if isinstance(file, list):
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Path is a directory: {path}"
                )
            
            content = file.decoded_content.decode('utf-8')
            
            return ToolResult(
                success=True,
                output={
                    "path": path,
                    "content": content,
                    "sha": file.sha,
                    "size": file.size,
                    "url": file.html_url
                },
                metadata={
                    "operation": "get_file",
                    "branch": branch,
                    "encoding": file.encoding
                }
            )
            
        except GithubException as e:
            if e.status == 404:
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"File not found: {path}"
                )
            return ToolResult(
                success=False,
                output=None,
                error=f"GitHub API error: {e.data.get('message', str(e))}"
            )
    
    async def _list_files(self, **kwargs) -> ToolResult:
        """List files in a directory."""
        try:
            if not self.repo:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Repository not available"
                )
            
            path = kwargs.get("path", "")
            branch = kwargs.get("branch", "main")
            
            # Get directory contents
            contents = self.repo.get_contents(path, ref=branch)
            
            if not isinstance(contents, list):
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Path is not a directory: {path}"
                )
            
            files = []
            for item in contents:
                files.append({
                    "name": item.name,
                    "path": item.path,
                    "type": item.type,
                    "size": item.size,
                    "sha": item.sha,
                    "url": item.html_url
                })
            
            return ToolResult(
                success=True,
                output=files,
                metadata={
                    "operation": "list_files",
                    "path": path,
                    "branch": branch,
                    "count": len(files)
                }
            )
            
        except GithubException as e:
            if e.status == 404:
                return ToolResult(
                    success=False,
                    output=None,
                    error=f"Directory not found: {path}"
                )
            return ToolResult(
                success=False,
                output=None,
                error=f"GitHub API error: {e.data.get('message', str(e))}"
            )
    
    async def _create_branch(self, **kwargs) -> ToolResult:
        """Create a new branch."""
        try:
            if not self.repo:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Repository not available"
                )
            
            branch_name = kwargs.get("branch_name", "")
            source_branch = kwargs.get("source_branch", "main")
            
            if not branch_name:
                return ToolResult(
                    success=False,
                    output=None,
                    error="Branch name is required"
                )
            
            # Get source branch SHA
            source_ref = self.repo.get_git_ref(f"heads/{source_branch}")
            source_sha = source_ref.object.sha
            
            # Create new branch
            new_ref = self.repo.create_git_ref(
                ref=f"refs/heads/{branch_name}",
                sha=source_sha
            )
            
            return ToolResult(
                success=True,
                output={
                    "branch_name": branch_name,
                    "sha": new_ref.object.sha,
                    "url": new_ref.url
                },
                metadata={
                    "operation": "create_branch",
                    "source_branch": source_branch
                }
            )
            
        except GithubException as e:
            return ToolResult(
                success=False,
                output=None,
                error=f"GitHub API error: {e.data.get('message', str(e))}"
            )
    
    def get_info(self) -> ToolInfo:
        """Get tool information."""
        return ToolInfo(
            name=self.name,
            tool_type=self.tool_type,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "description": "GitHub operation to perform",
                        "enum": [
                            "create_file", "update_file", "get_file", "list_files",
                            "create_branch", "create_pr", "create_issue", "commit_multiple"
                        ]
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    },
                    "content": {
                        "type": "string",
                        "description": "File content"
                    },
                    "message": {
                        "type": "string",
                        "description": "Commit message"
                    },
                    "branch": {
                        "type": "string",
                        "description": "Branch name",
                        "default": "main"
                    }
                },
                "required": ["operation"]
            },
            required_permissions=["github_access"],
            supported_agents=[AgentRole.DEVELOPER, AgentRole.PROJECT_MANAGER]
        )
    
    def validate_parameters(self, **kwargs) -> bool:
        """Validate parameters."""
        operation = kwargs.get("operation")
        if not operation:
            return False
        
        # Validate operation-specific parameters
        if operation in ["create_file", "update_file"]:
            return "path" in kwargs and "content" in kwargs
        elif operation in ["get_file", "list_files"]:
            return "path" in kwargs
        elif operation == "create_branch":
            return "branch_name" in kwargs
        
        return True
