"""
Centralized logging system for the Multi-Agent Development System.

This module provides a structured logging system with support for different
log levels, formatters, and output destinations.
"""

import json
import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union

import structlog
from rich.console import Console
from rich.logging import RichHandler

from ..models.enums import LogLevel, AgentRole


class AgentLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter that adds agent context to log records."""
    
    def __init__(self, logger: logging.Logger, agent_id: str, agent_role: AgentRole):
        self.agent_id = agent_id
        self.agent_role = agent_role
        super().__init__(logger, {})
    
    def process(self, msg: str, kwargs: Dict[str, Any]) -> tuple[str, Dict[str, Any]]:
        """Add agent context to log records."""
        extra = kwargs.get('extra', {})
        extra.update({
            'agent_id': self.agent_id,
            'agent_role': self.agent_role.value,
            'timestamp': datetime.utcnow().isoformat(),
        })
        kwargs['extra'] = extra
        return msg, kwargs


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_data = {
            'timestamp': datetime.utcfromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add extra fields
        if hasattr(record, 'agent_id'):
            log_data['agent_id'] = record.agent_id
        if hasattr(record, 'agent_role'):
            log_data['agent_role'] = record.agent_role
        if hasattr(record, 'task_id'):
            log_data['task_id'] = record.task_id
        if hasattr(record, 'message_id'):
            log_data['message_id'] = record.message_id
        
        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Add stack info if present
        if record.stack_info:
            log_data['stack_info'] = record.stack_info
        
        return json.dumps(log_data, default=str)


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output."""
    
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        color = self.COLORS.get(record.levelname, '')
        reset = self.RESET
        
        # Format timestamp
        timestamp = datetime.utcfromtimestamp(record.created).strftime('%H:%M:%S')
        
        # Format agent info
        agent_info = ""
        if hasattr(record, 'agent_id') and hasattr(record, 'agent_role'):
            agent_info = f"[{record.agent_role}:{record.agent_id[:8]}] "
        
        # Format message
        message = record.getMessage()
        
        return f"{color}[{timestamp}] {record.levelname:8} {agent_info}{message}{reset}"


class LoggerManager:
    """Centralized logger management."""
    
    _instance: Optional['LoggerManager'] = None
    _loggers: Dict[str, logging.Logger] = {}
    _initialized: bool = False
    
    def __new__(cls) -> 'LoggerManager':
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize logger manager."""
        if not self._initialized:
            self._setup_logging()
            self._initialized = True
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        # Get configuration from environment
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_format = os.getenv('LOG_FORMAT', 'colored')  # 'json' or 'colored'
        log_file_path = os.getenv('LOG_FILE_PATH', './logs/agents.log')
        
        # Create logs directory
        log_dir = Path(log_file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        if log_format == 'json':
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(JSONFormatter())
        else:
            # Use Rich handler for colored output
            console = Console()
            console_handler = RichHandler(
                console=console,
                show_time=True,
                show_path=True,
                markup=True,
                rich_tracebacks=True
            )
        
        console_handler.setLevel(getattr(logging, log_level))
        root_logger.addHandler(console_handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_file_path,
            maxBytes=100 * 1024 * 1024,  # 100MB
            backupCount=5
        )
        file_handler.setFormatter(JSONFormatter())
        file_handler.setLevel(logging.DEBUG)  # Always log everything to file
        root_logger.addHandler(file_handler)
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger."""
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        return self._loggers[name]
    
    def get_agent_logger(self, agent_id: str, agent_role: AgentRole) -> AgentLoggerAdapter:
        """Get a logger adapter for an agent."""
        logger_name = f"agent.{agent_role.value}.{agent_id}"
        base_logger = self.get_logger(logger_name)
        return AgentLoggerAdapter(base_logger, agent_id, agent_role)
    
    def get_system_logger(self) -> logging.Logger:
        """Get system logger."""
        return self.get_logger("system")
    
    def get_communication_logger(self) -> logging.Logger:
        """Get communication logger."""
        return self.get_logger("communication")
    
    def get_task_logger(self) -> logging.Logger:
        """Get task logger."""
        return self.get_logger("task")
    
    def set_level(self, level: Union[str, int]) -> None:
        """Set logging level for all loggers."""
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        
        for logger in self._loggers.values():
            logger.setLevel(level)
        
        # Also set for root logger
        logging.getLogger().setLevel(level)


# Global logger manager instance
logger_manager = LoggerManager()

# Convenience functions
def get_logger(name: str) -> logging.Logger:
    """Get a logger by name."""
    return logger_manager.get_logger(name)


def get_agent_logger(agent_id: str, agent_role: AgentRole) -> AgentLoggerAdapter:
    """Get an agent logger."""
    return logger_manager.get_agent_logger(agent_id, agent_role)


def get_system_logger() -> logging.Logger:
    """Get system logger."""
    return logger_manager.get_system_logger()


def get_communication_logger() -> logging.Logger:
    """Get communication logger."""
    return logger_manager.get_communication_logger()


def get_task_logger() -> logging.Logger:
    """Get task logger."""
    return logger_manager.get_task_logger()


def set_log_level(level: Union[str, int]) -> None:
    """Set global log level."""
    logger_manager.set_level(level)


# Structured logging helpers
def log_agent_action(
    agent_id: str,
    agent_role: AgentRole,
    action: str,
    details: Optional[Dict[str, Any]] = None,
    level: str = "INFO"
) -> None:
    """Log an agent action with structured data."""
    logger = get_agent_logger(agent_id, agent_role)
    log_data = {
        'action': action,
        'details': details or {}
    }
    
    getattr(logger, level.lower())(
        f"Agent action: {action}",
        extra=log_data
    )


def log_task_event(
    task_id: str,
    event: str,
    agent_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    level: str = "INFO"
) -> None:
    """Log a task event with structured data."""
    logger = get_task_logger()
    log_data = {
        'task_id': task_id,
        'event': event,
        'agent_id': agent_id,
        'details': details or {}
    }
    
    getattr(logger, level.lower())(
        f"Task event: {event}",
        extra=log_data
    )


def log_communication_event(
    event: str,
    sender_id: Optional[str] = None,
    receiver_id: Optional[str] = None,
    message_type: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    level: str = "INFO"
) -> None:
    """Log a communication event with structured data."""
    logger = get_communication_logger()
    log_data = {
        'event': event,
        'sender_id': sender_id,
        'receiver_id': receiver_id,
        'message_type': message_type,
        'details': details or {}
    }
    
    getattr(logger, level.lower())(
        f"Communication event: {event}",
        extra=log_data
    )


def log_system_event(
    event: str,
    component: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    level: str = "INFO"
) -> None:
    """Log a system event with structured data."""
    logger = get_system_logger()
    log_data = {
        'event': event,
        'component': component,
        'details': details or {}
    }
    
    getattr(logger, level.lower())(
        f"System event: {event}",
        extra=log_data
    )


# Performance logging
class PerformanceLogger:
    """Performance logging context manager."""
    
    def __init__(self, operation: str, logger: logging.Logger, threshold_ms: float = 1000.0):
        self.operation = operation
        self.logger = logger
        self.threshold_ms = threshold_ms
        self.start_time: Optional[float] = None
    
    def __enter__(self) -> 'PerformanceLogger':
        """Start timing."""
        import time
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """End timing and log if over threshold."""
        if self.start_time is not None:
            import time
            duration_ms = (time.time() - self.start_time) * 1000
            
            if duration_ms > self.threshold_ms:
                self.logger.warning(
                    f"Slow operation: {self.operation}",
                    extra={
                        'operation': self.operation,
                        'duration_ms': duration_ms,
                        'threshold_ms': self.threshold_ms
                    }
                )
            else:
                self.logger.debug(
                    f"Operation completed: {self.operation}",
                    extra={
                        'operation': self.operation,
                        'duration_ms': duration_ms
                    }
                )


def log_performance(operation: str, logger: Optional[logging.Logger] = None, threshold_ms: float = 1000.0) -> PerformanceLogger:
    """Create a performance logging context manager."""
    if logger is None:
        logger = get_system_logger()
    return PerformanceLogger(operation, logger, threshold_ms)
