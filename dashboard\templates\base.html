<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Multi-Agent Development System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-bottom: 1px solid #e2e8f0;
            border-radius: 12px 12px 0 0 !important;
            font-weight: 600;
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-weight: 500;
        }
        
        .status-online {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-offline {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .status-working {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .status-idle {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border-left: 4px solid var(--primary-color);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .metric-label {
            color: var(--secondary-color);
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .agent-card {
            transition: all 0.3s ease;
        }
        
        .agent-card:hover {
            border-color: var(--primary-color);
        }
        
        .message-item {
            border-left: 3px solid #e2e8f0;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        
        .message-item.type-task {
            border-left-color: var(--primary-color);
        }
        
        .message-item.type-notification {
            border-left-color: var(--success-color);
        }
        
        .message-item.type-error {
            border-left-color: var(--danger-color);
        }
        
        .sidebar {
            background: linear-gradient(180deg, #ffffff, #f8fafc);
            border-right: 1px solid #e2e8f0;
            min-height: calc(100vh - 76px);
        }
        
        .nav-link {
            color: var(--secondary-color);
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.2s;
        }
        
        .nav-link:hover, .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .connection-status {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1000;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .connection-connected {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .connection-disconnected {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot me-2"></i>
                Multi-Agent System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/agents' %}active{% endif %}" href="/agents">
                            <i class="fas fa-users me-1"></i>Agents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.url.path == '/messages' %}active{% endif %}" href="/messages">
                            <i class="fas fa-comments me-1"></i>Messages
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time">{{ timestamp }}</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Connection Status -->
    <div id="connection-status" class="connection-status connection-disconnected" style="display: none;">
        <i class="fas fa-wifi me-1"></i>
        <span id="connection-text">Connecting...</span>
    </div>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global variables
        let socket = null;
        let connectionStatus = document.getElementById('connection-status');
        let connectionText = document.getElementById('connection-text');
        
        // Initialize WebSocket connection
        function initWebSocket() {
            try {
                socket = io();
                
                socket.on('connect', function() {
                    updateConnectionStatus(true);
                    console.log('Connected to server');
                });
                
                socket.on('disconnect', function() {
                    updateConnectionStatus(false);
                    console.log('Disconnected from server');
                });
                
                socket.on('statistics_update', function(data) {
                    updateStatistics(data.stats);
                });
                
                socket.on('agents_update', function(data) {
                    updateAgents(data.agents);
                });
                
                socket.on('new_message', function(data) {
                    addNewMessage(data);
                });
                
            } catch (error) {
                console.error('WebSocket initialization failed:', error);
                updateConnectionStatus(false);
            }
        }
        
        // Update connection status
        function updateConnectionStatus(connected) {
            connectionStatus.style.display = 'block';
            
            if (connected) {
                connectionStatus.className = 'connection-status connection-connected';
                connectionText.innerHTML = '<i class="fas fa-check me-1"></i>Connected';
            } else {
                connectionStatus.className = 'connection-status connection-disconnected';
                connectionText.innerHTML = '<i class="fas fa-times me-1"></i>Disconnected';
            }
        }
        
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        
        // Format timestamp
        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }
        
        // Format duration
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = Math.floor(seconds % 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes}m ${secs}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }
        
        // Get status badge HTML
        function getStatusBadge(status) {
            const statusMap = {
                'online': { class: 'status-online', icon: 'check-circle', text: 'Online' },
                'offline': { class: 'status-offline', icon: 'times-circle', text: 'Offline' },
                'working': { class: 'status-working', icon: 'cog', text: 'Working' },
                'idle': { class: 'status-idle', icon: 'pause-circle', text: 'Idle' },
                'error': { class: 'status-offline', icon: 'exclamation-triangle', text: 'Error' }
            };
            
            const statusInfo = statusMap[status.toLowerCase()] || statusMap['offline'];
            return `<span class="status-badge ${statusInfo.class}">
                        <i class="fas fa-${statusInfo.icon} me-1"></i>${statusInfo.text}
                    </span>`;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            updateTime();
            setInterval(updateTime, 1000);
        });
        
        // Page-specific functions (to be overridden)
        function updateStatistics(stats) {
            // Override in specific pages
        }
        
        function updateAgents(agents) {
            // Override in specific pages
        }
        
        function addNewMessage(message) {
            // Override in specific pages
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
