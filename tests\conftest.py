"""
Pytest configuration and fixtures for the Multi-Agent Development System tests.

This module provides common test fixtures and configuration for all test modules.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import Dict, Any, Generator
from unittest.mock import Mock, AsyncMock

import pytest
import pytest_asyncio

# Set test environment
os.environ["TESTING"] = "true"
os.environ["LOG_LEVEL"] = "DEBUG"

from shared.models import (
    Agent, AgentRole, AgentStatus, AgentCapabilities, AgentConfiguration,
    Task, TaskType, TaskStatus, TaskPriority,
    Message, MessageType, LLMProvider
)
from shared.utils import config
from shared.llm import LLMClient
from shared.tools import ToolManager
from agents.base_agent import BaseAgent


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_config():
    """Mock configuration for tests."""
    test_config = {
        "openai_api_key": "test_openai_key",
        "anthropic_api_key": "test_anthropic_key",
        "github_token": "test_github_token",
        "github_username": "test_user",
        "redis_url": "redis://localhost:6379/1",
        "log_level": "DEBUG",
        "testing": True,
    }
    
    # Update config with test values
    original_config = {}
    for key, value in test_config.items():
        original_config[key] = config.get(key)
        config.set(key, value)
    
    yield test_config
    
    # Restore original config
    for key, value in original_config.items():
        if value is not None:
            config.set(key, value)


@pytest.fixture
def sample_agent_config():
    """Sample agent configuration for tests."""
    return AgentConfiguration(
        llm_provider=LLMProvider.OPENAI,
        model_name="gpt-4-turbo",
        temperature=0.3,
        max_tokens=2000,
        timeout_seconds=30,
        max_retries=3,
        heartbeat_interval=30,
        enable_memory=True,
        enable_tools=True,
        enable_collaboration=True
    )


@pytest.fixture
def sample_agent_capabilities():
    """Sample agent capabilities for tests."""
    return AgentCapabilities(
        skills=["python", "testing", "debugging"],
        tools=["code_generator", "file_manager"],
        languages=["python", "javascript"],
        frameworks=["pytest", "fastapi"],
        max_concurrent_tasks=2
    )


@pytest.fixture
def sample_agent(sample_agent_config, sample_agent_capabilities):
    """Create a sample agent for tests."""
    return Agent(
        name="Test Agent",
        role=AgentRole.DEVELOPER,
        description="A test agent for unit testing",
        capabilities=sample_agent_capabilities,
        configuration=sample_agent_config
    )


@pytest.fixture
def sample_task():
    """Create a sample task for tests."""
    return Task(
        title="Test Task",
        description="A test task for unit testing",
        task_type=TaskType.DEVELOPMENT,
        created_by="test_agent",
        created_by_role=AgentRole.PROJECT_MANAGER,
        priority=TaskPriority.MEDIUM
    )


@pytest.fixture
def sample_message():
    """Create a sample message for tests."""
    return Message(
        sender_id="test_sender",
        sender_role=AgentRole.PROJECT_MANAGER,
        receiver_id="test_receiver",
        receiver_role=AgentRole.DEVELOPER,
        message_type=MessageType.TASK,
        content="Test message content"
    )


@pytest.fixture
def mock_llm_client():
    """Mock LLM client for tests."""
    mock_client = Mock(spec=LLMClient)
    mock_client.generate = AsyncMock()
    mock_client.generate_code = AsyncMock()
    mock_client.analyze_code = AsyncMock()
    mock_client.validate_config = AsyncMock(return_value={LLMProvider.OPENAI: True})
    
    # Default mock responses
    from shared.llm.client import LLMResponse
    mock_client.generate.return_value = LLMResponse(
        content="Mock LLM response",
        model="gpt-4-turbo",
        provider="openai",
        usage={"total_tokens": 100},
        metadata={}
    )
    
    mock_client.generate_code.return_value = LLMResponse(
        content="def mock_function():\n    pass",
        model="gpt-4-turbo",
        provider="openai",
        usage={"total_tokens": 50},
        metadata={}
    )
    
    return mock_client


@pytest.fixture
def mock_tool_manager():
    """Mock tool manager for tests."""
    mock_manager = Mock(spec=ToolManager)
    mock_manager.execute_tool = AsyncMock()
    mock_manager.get_available_tools = Mock(return_value=["code_generator", "file_manager"])
    mock_manager.get_tool_schemas = Mock(return_value={})
    
    # Default mock tool results
    from shared.tools.manager import ToolResult
    mock_manager.execute_tool.return_value = ToolResult(
        success=True,
        output="Mock tool output",
        metadata={"tool": "mock"}
    )
    
    return mock_manager


@pytest.fixture
def mock_communication_hub():
    """Mock communication hub for tests."""
    mock_hub = Mock()
    mock_hub.send_message = AsyncMock()
    mock_hub.register_agent = AsyncMock()
    mock_hub.unregister_agent = AsyncMock()
    return mock_hub


class MockBaseAgent(BaseAgent):
    """Mock implementation of BaseAgent for testing."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.processed_tasks = []
    
    async def process_task(self, task: Task):
        """Mock task processing."""
        from shared.models.task import TaskResult
        
        self.processed_tasks.append(task)
        
        return TaskResult(
            success=True,
            output={"result": f"Processed task: {task.title}"},
            logs=[f"Processing task {task.id}"],
            duration_seconds=1.0
        )
    
    async def get_capabilities(self):
        """Mock capabilities."""
        return self.agent.capabilities


@pytest.fixture
def mock_base_agent(sample_agent_config, sample_agent_capabilities):
    """Create a mock base agent for tests."""
    return MockBaseAgent(
        name="Mock Agent",
        role=AgentRole.DEVELOPER,
        description="Mock agent for testing",
        capabilities=sample_agent_capabilities,
        configuration=sample_agent_config
    )


@pytest.fixture
def redis_client():
    """Redis client for integration tests."""
    try:
        import redis
        client = redis.Redis.from_url("redis://localhost:6379/1", decode_responses=True)
        
        # Test connection
        client.ping()
        
        # Clear test database
        client.flushdb()
        
        yield client
        
        # Cleanup
        client.flushdb()
        client.close()
        
    except Exception:
        pytest.skip("Redis not available for integration tests")


@pytest.fixture
def github_mock():
    """Mock GitHub client for tests."""
    mock_github = Mock()
    mock_repo = Mock()
    
    # Mock repository operations
    mock_repo.create_file.return_value = {
        "commit": Mock(sha="test_sha"),
        "content": Mock(html_url="https://github.com/test/repo/blob/main/test.py")
    }
    
    mock_repo.get_contents.return_value = Mock(
        decoded_content=b"test content",
        sha="test_sha",
        size=12,
        html_url="https://github.com/test/repo/blob/main/test.py"
    )
    
    mock_github.get_repo.return_value = mock_repo
    
    return mock_github


# Test markers
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.slow = pytest.mark.slow


# Test utilities
def assert_agent_status(agent: Agent, expected_status: AgentStatus):
    """Assert agent has expected status."""
    assert agent.state.status == expected_status, f"Expected {expected_status}, got {agent.state.status}"


def assert_task_status(task: Task, expected_status: TaskStatus):
    """Assert task has expected status."""
    assert task.status == expected_status, f"Expected {expected_status}, got {task.status}"


def assert_message_type(message: Message, expected_type: MessageType):
    """Assert message has expected type."""
    assert message.message_type == expected_type, f"Expected {expected_type}, got {message.message_type}"


# Async test helpers
async def wait_for_condition(condition_func, timeout=5.0, interval=0.1):
    """Wait for a condition to become true."""
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if condition_func():
            return True
        await asyncio.sleep(interval)
    
    return False


# Mock data generators
def generate_test_tasks(count: int = 5) -> list[Task]:
    """Generate test tasks."""
    tasks = []
    for i in range(count):
        task = Task(
            title=f"Test Task {i+1}",
            description=f"Description for test task {i+1}",
            task_type=TaskType.DEVELOPMENT,
            created_by="test_agent",
            created_by_role=AgentRole.PROJECT_MANAGER,
            priority=TaskPriority.MEDIUM
        )
        tasks.append(task)
    return tasks


def generate_test_messages(count: int = 5) -> list[Message]:
    """Generate test messages."""
    messages = []
    for i in range(count):
        message = Message(
            sender_id=f"sender_{i}",
            sender_role=AgentRole.PROJECT_MANAGER,
            receiver_id=f"receiver_{i}",
            receiver_role=AgentRole.DEVELOPER,
            message_type=MessageType.NOTIFICATION,
            content=f"Test message {i+1}"
        )
        messages.append(message)
    return messages
