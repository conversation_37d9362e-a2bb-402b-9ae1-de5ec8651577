"""
Test Agent for the Multi-Agent Development System.

This module implements the Test Agent responsible for test generation,
execution, and quality assurance in the multi-agent development environment.
"""

import asyncio
import subprocess
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path

from shared.models import (
    AgentRole, AgentCapabilities, AgentConfiguration, TaskType, TaskPriority,
    TaskStatus, Task, TaskResult, Message, MessageType, LLMProvider, ToolType,
    create_task, create_response_message, create_notification_message
)
from shared.utils import get_agent_logger, log_agent_action, get_config
from shared.llm import LLMClient
from shared.tools import ToolManager
from .base_agent import BaseAgent


class TestAgent(BaseAgent):
    """
    Test Agent responsible for:
    - Test case generation and implementation
    - Test execution and reporting
    - Code coverage analysis
    - Quality assurance and validation
    - Performance testing
    - Integration testing
    - Bug detection and reporting
    """
    
    def __init__(self, name: str = "Test Agent", **kwargs):
        """Initialize Test Agent."""
        
        # Define Test-specific capabilities
        capabilities = AgentCapabilities(
            skills=[
                "test_design", "test_automation", "unit_testing", "integration_testing",
                "performance_testing", "security_testing", "api_testing", "ui_testing",
                "test_data_management", "bug_reporting", "quality_assurance",
                "code_coverage", "test_documentation", "test_maintenance"
            ],
            tools=[
                ToolType.TEST_RUNNER, ToolType.CODE_ANALYZER, ToolType.FILE_MANAGER,
                ToolType.DEBUGGER, ToolType.PERFORMANCE_MONITOR, ToolType.DOCUMENTATION_GENERATOR
            ],
            languages=[
                "python", "javascript", "typescript", "sql", "bash", "yaml", "json"
            ],
            frameworks=[
                "pytest", "unittest", "jest", "selenium", "playwright", "postman",
                "locust", "coverage", "tox", "behave"
            ],
            max_concurrent_tasks=4
        )
        
        # Define Test-specific configuration
        configuration = AgentConfiguration(
            llm_provider=LLMProvider.OPENAI,
            model_name=get_config("test_agent_model", "gpt-4"),
            temperature=get_config("test_agent_temperature", 0.2),
            max_tokens=get_config("test_agent_max_tokens", 1500),
            timeout_seconds=60,
            max_retries=3,
            enable_memory=True,
            enable_tools=True,
            enable_collaboration=True
        )
        
        super().__init__(
            name=name,
            role=AgentRole.TESTER,
            description="Test Agent responsible for test generation, execution, and quality assurance",
            capabilities=capabilities,
            configuration=configuration,
            **kwargs
        )
        
        # Test-specific state
        self.test_results: Dict[str, Dict[str, Any]] = {}
        self.test_suites: Dict[str, List[str]] = {}
        self.coverage_reports: Dict[str, Dict[str, Any]] = {}
        self.test_templates: Dict[str, str] = {}
        
        # Initialize test templates
        self._initialize_test_templates()
        
        self.logger.info("Test Agent initialized")
    
    def _initialize_test_templates(self) -> None:
        """Initialize test templates for common testing patterns."""
        self.test_templates = {
            "unit_test": '''"""
Unit tests for {module_name}.

This module contains unit tests for the {module_name} functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from {module_path} import {class_name}

class Test{class_name}:
    """Test {class_name} class."""
    
    def setup_method(self):
        """Setup test fixtures before each test method."""
        self.{instance_name} = {class_name}()
    
    def teardown_method(self):
        """Clean up after each test method."""
        pass
    
    def test_initialization(self):
        """Test {class_name} initialization."""
        assert self.{instance_name} is not None
        # Add specific initialization tests here
    
    @pytest.mark.asyncio
    async def test_async_method(self):
        """Test async methods."""
        # Test async functionality here
        pass
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Test edge cases here
        pass
    
    def test_error_handling(self):
        """Test error handling and exception cases."""
        # Test error conditions here
        pass
    
    @pytest.mark.parametrize("input_data,expected", [
        ("test_input_1", "expected_output_1"),
        ("test_input_2", "expected_output_2"),
    ])
    def test_parametrized(self, input_data, expected):
        """Test with multiple input parameters."""
        # Parametrized test implementation
        pass
''',
            
            "api_test": '''"""
API tests for {api_name}.

This module contains API integration tests.
"""

import pytest
import httpx
import asyncio
from typing import Dict, Any

class Test{api_name}API:
    """Test {api_name} API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return httpx.AsyncClient(base_url="http://localhost:8000")
    
    @pytest.mark.asyncio
    async def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = await client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_create_item(self, client):
        """Test item creation endpoint."""
        item_data = {{
            "title": "Test Item",
            "description": "Test Description"
        }}
        
        response = await client.post("/items/", json=item_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["title"] == item_data["title"]
        assert data["description"] == item_data["description"]
        assert "id" in data
    
    @pytest.mark.asyncio
    async def test_get_items(self, client):
        """Test get items endpoint."""
        response = await client.get("/items/")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_invalid_request(self, client):
        """Test invalid request handling."""
        response = await client.post("/items/", json={{}})
        assert response.status_code == 422  # Validation error
''',
            
            "performance_test": '''"""
Performance tests for {module_name}.

This module contains performance and load tests.
"""

import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import statistics

class TestPerformance:
    """Performance tests for {module_name}."""
    
    def test_response_time(self):
        """Test response time is within acceptable limits."""
        start_time = time.time()
        
        # Execute the operation to test
        # result = your_function()
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # Assert response time is under threshold (e.g., 1 second)
        assert response_time < 1.0, f"Response time {{response_time:.3f}}s exceeds threshold"
    
    def test_concurrent_operations(self):
        """Test system under concurrent load."""
        num_threads = 10
        num_operations = 100
        
        def perform_operation():
            # Your operation here
            time.sleep(0.01)  # Simulate work
            return True
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(perform_operation) for _ in range(num_operations)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # All operations should succeed
        assert all(results), "Some operations failed under load"
        
        # Check throughput
        throughput = num_operations / total_time
        assert throughput > 50, f"Throughput {{throughput:.2f}} ops/sec is too low"
    
    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """Test memory usage stays within limits."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Perform memory-intensive operation
        # your_memory_intensive_function()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Assert memory increase is reasonable (e.g., under 100MB)
        assert memory_increase < 100, f"Memory increase {{memory_increase:.2f}}MB is too high"
'''
        }
    
    async def process_task(self, task: Task) -> TaskResult:
        """Process Test-specific tasks."""
        try:
            self.logger.info(f"Processing Test task: {task.title}")
            
            # Determine task type and route to appropriate handler
            task_lower = task.title.lower()
            description_lower = task.description.lower()
            
            if any(keyword in task_lower for keyword in ["generate", "create", "write"]) and "test" in task_lower:
                return await self._handle_test_generation_task(task)
            elif any(keyword in task_lower for keyword in ["run", "execute", "perform"]) and "test" in task_lower:
                return await self._handle_test_execution_task(task)
            elif any(keyword in task_lower for keyword in ["coverage", "analyze"]):
                return await self._handle_coverage_analysis_task(task)
            elif any(keyword in task_lower for keyword in ["performance", "load", "stress"]):
                return await self._handle_performance_test_task(task)
            elif any(keyword in task_lower for keyword in ["api", "integration"]):
                return await self._handle_integration_test_task(task)
            elif any(keyword in task_lower for keyword in ["report", "summary"]):
                return await self._handle_test_reporting_task(task)
            else:
                return await self._handle_general_test_task(task)
                
        except Exception as e:
            self.logger.error(f"Error processing Test task: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Test task processing failed: {e}"]
            )
    
    async def _handle_test_generation_task(self, task: Task) -> TaskResult:
        """Handle test generation tasks."""
        try:
            self.logger.info(f"Handling test generation task: {task.title}")
            
            # Extract test requirements
            test_type = task.get_context("test_type", "unit")
            target_code = task.get_context("target_code", "")
            target_file = task.get_context("target_file", "")
            test_framework = task.get_context("framework", "pytest")
            
            # Read target code if file path provided
            if target_file and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="read",
                    path=target_file
                )
                if file_result.success:
                    target_code = file_result.output
            
            # Generate test code using LLM
            test_prompt = f"""
            As a Senior QA Engineer, generate comprehensive {test_type} tests for: {task.title}
            
            Requirements: {task.description}
            Target Code: {target_code}
            Test Framework: {test_framework}
            
            Please provide:
            1. Complete test class with proper setup/teardown
            2. Comprehensive test cases covering:
               - Happy path scenarios
               - Edge cases and boundary conditions
               - Error handling and exception cases
               - Input validation
            3. Mock usage where appropriate
            4. Parametrized tests for multiple scenarios
            5. Clear test documentation and assertions
            
            Generate production-ready, maintainable test code.
            """
            
            llm_response = await self.llm_client.generate_code(
                prompt=test_prompt,
                language="python",
                context=f"Test framework: {test_framework}, Type: {test_type}"
            )
            
            if not llm_response.success:
                raise Exception(f"Test generation failed: {llm_response.error}")
            
            test_code = llm_response.content
            
            # Save test file if requested
            test_file_path = task.get_context("test_file_path", "")
            if test_file_path and self.tool_manager:
                file_result = await self.tool_manager.execute_tool(
                    "file_manager",
                    operation="write",
                    path=test_file_path,
                    content=test_code
                )
                
                if file_result.success:
                    self.logger.info(f"Test file saved: {test_file_path}")
            
            # Store test in suite
            suite_name = task.get_context("suite_name", "default")
            if suite_name not in self.test_suites:
                self.test_suites[suite_name] = []
            self.test_suites[suite_name].append(test_file_path or "generated_test")
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "test_generated",
                {
                    "test_type": test_type,
                    "framework": test_framework,
                    "test_file": test_file_path,
                    "code_length": len(test_code)
                }
            )
            
            return TaskResult(
                success=True,
                output={
                    "test_code": test_code,
                    "test_type": test_type,
                    "test_framework": test_framework,
                    "test_file_path": test_file_path,
                    "suite_name": suite_name
                },
                logs=[f"Test code generated for {task.title}"],
                duration_seconds=3.0
            )
            
        except Exception as e:
            self.logger.error(f"Test generation task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Test generation failed: {e}"]
            )
    
    async def _handle_test_execution_task(self, task: Task) -> TaskResult:
        """Handle test execution tasks."""
        try:
            self.logger.info(f"Handling test execution task: {task.title}")
            
            test_path = task.get_context("test_path", "tests/")
            test_framework = task.get_context("framework", "pytest")
            test_args = task.get_context("args", [])
            
            # Execute tests
            if test_framework == "pytest":
                result = await self._run_pytest(test_path, test_args)
            elif test_framework == "unittest":
                result = await self._run_unittest(test_path)
            else:
                raise Exception(f"Unsupported test framework: {test_framework}")
            
            # Store test results
            test_id = f"test_run_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            self.test_results[test_id] = {
                "test_path": test_path,
                "framework": test_framework,
                "result": result,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "tests_executed",
                {
                    "test_id": test_id,
                    "test_path": test_path,
                    "framework": test_framework,
                    "success": result["success"],
                    "tests_run": result.get("tests_run", 0)
                }
            )
            
            return TaskResult(
                success=result["success"],
                output={
                    "test_id": test_id,
                    "test_results": result,
                    "summary": self._generate_test_summary(result)
                },
                logs=[f"Tests executed: {result.get('tests_run', 0)} tests"],
                duration_seconds=result.get("duration", 0)
            )
            
        except Exception as e:
            self.logger.error(f"Test execution task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Test execution failed: {e}"]
            )
    
    async def _run_pytest(self, test_path: str, args: List[str]) -> Dict[str, Any]:
        """Run pytest and return results."""
        try:
            import subprocess
            import time
            
            # Build pytest command
            cmd = ["python", "-m", "pytest", test_path, "-v", "--tb=short"] + args
            
            start_time = time.time()
            
            # Run pytest
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            stdout, stderr = await process.communicate()
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Parse results
            output = stdout.decode() if stdout else ""
            error_output = stderr.decode() if stderr else ""
            
            # Extract test statistics
            tests_run = 0
            tests_passed = 0
            tests_failed = 0
            
            # Simple parsing of pytest output
            if "failed" in output.lower():
                failed_match = re.search(r'(\d+) failed', output)
                if failed_match:
                    tests_failed = int(failed_match.group(1))
            
            if "passed" in output.lower():
                passed_match = re.search(r'(\d+) passed', output)
                if passed_match:
                    tests_passed = int(passed_match.group(1))
            
            tests_run = tests_passed + tests_failed
            
            return {
                "success": process.returncode == 0,
                "return_code": process.returncode,
                "tests_run": tests_run,
                "tests_passed": tests_passed,
                "tests_failed": tests_failed,
                "duration": duration,
                "output": output,
                "error_output": error_output
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tests_run": 0,
                "tests_passed": 0,
                "tests_failed": 0,
                "duration": 0
            }
    
    async def _run_unittest(self, test_path: str) -> Dict[str, Any]:
        """Run unittest and return results."""
        try:
            import subprocess
            import time
            
            cmd = ["python", "-m", "unittest", "discover", "-s", test_path, "-v"]
            
            start_time = time.time()
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            stdout, stderr = await process.communicate()
            
            end_time = time.time()
            duration = end_time - start_time
            
            output = stdout.decode() if stdout else ""
            error_output = stderr.decode() if stderr else ""
            
            return {
                "success": process.returncode == 0,
                "return_code": process.returncode,
                "duration": duration,
                "output": output,
                "error_output": error_output
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "duration": 0
            }
    
    def _generate_test_summary(self, result: Dict[str, Any]) -> str:
        """Generate a human-readable test summary."""
        if not result["success"]:
            return f"Tests failed with return code {result.get('return_code', 'unknown')}"
        
        tests_run = result.get("tests_run", 0)
        tests_passed = result.get("tests_passed", 0)
        tests_failed = result.get("tests_failed", 0)
        duration = result.get("duration", 0)
        
        summary = f"Executed {tests_run} tests in {duration:.2f}s"
        if tests_passed > 0:
            summary += f", {tests_passed} passed"
        if tests_failed > 0:
            summary += f", {tests_failed} failed"
        
        return summary
    
    async def _handle_coverage_analysis_task(self, task: Task) -> TaskResult:
        """Handle code coverage analysis tasks."""
        try:
            self.logger.info(f"Handling coverage analysis task: {task.title}")
            
            # Generate coverage analysis using LLM
            coverage_prompt = f"""
            As a QA Engineer, analyze code coverage for: {task.title}
            
            Requirements: {task.description}
            
            Please provide:
            1. Coverage analysis strategy
            2. Key areas to focus on
            3. Coverage targets and thresholds
            4. Recommendations for improving coverage
            5. Tools and techniques to use
            """
            
            llm_response = await self.llm_client.generate(
                prompt=coverage_prompt,
                system_prompt="You are a QA engineer specializing in code coverage analysis."
            )
            
            if not llm_response.success:
                raise Exception(f"Coverage analysis failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "coverage_analysis": llm_response.content,
                    "analysis_type": "coverage"
                },
                logs=[f"Coverage analysis completed for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Coverage analysis task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Coverage analysis failed: {e}"]
            )
    
    async def _handle_performance_test_task(self, task: Task) -> TaskResult:
        """Handle performance testing tasks."""
        try:
            self.logger.info(f"Handling performance test task: {task.title}")
            
            # Generate performance test strategy
            perf_prompt = f"""
            As a Performance Testing Engineer, create a performance test plan for: {task.title}
            
            Requirements: {task.description}
            
            Please provide:
            1. Performance test strategy
            2. Key performance indicators (KPIs)
            3. Load testing scenarios
            4. Performance benchmarks and thresholds
            5. Tools and frameworks to use
            6. Test data requirements
            """
            
            llm_response = await self.llm_client.generate(
                prompt=perf_prompt,
                system_prompt="You are a performance testing engineer creating comprehensive test plans."
            )
            
            if not llm_response.success:
                raise Exception(f"Performance test planning failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "performance_plan": llm_response.content,
                    "test_type": "performance"
                },
                logs=[f"Performance test plan created for {task.title}"],
                duration_seconds=2.5
            )
            
        except Exception as e:
            self.logger.error(f"Performance test task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Performance test planning failed: {e}"]
            )
    
    async def _handle_integration_test_task(self, task: Task) -> TaskResult:
        """Handle integration testing tasks."""
        try:
            self.logger.info(f"Handling integration test task: {task.title}")
            
            # Generate integration test strategy
            integration_prompt = f"""
            As a QA Engineer, create integration tests for: {task.title}
            
            Requirements: {task.description}
            
            Please provide:
            1. Integration test strategy
            2. API endpoint testing approach
            3. Database integration tests
            4. External service mocking strategy
            5. Test data setup and teardown
            6. Error scenario testing
            """
            
            llm_response = await self.llm_client.generate(
                prompt=integration_prompt,
                system_prompt="You are a QA engineer specializing in integration testing."
            )
            
            if not llm_response.success:
                raise Exception(f"Integration test planning failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "integration_plan": llm_response.content,
                    "test_type": "integration"
                },
                logs=[f"Integration test plan created for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Integration test task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Integration test planning failed: {e}"]
            )
    
    async def _handle_test_reporting_task(self, task: Task) -> TaskResult:
        """Handle test reporting tasks."""
        try:
            self.logger.info(f"Handling test reporting task: {task.title}")
            
            # Gather test data
            test_data = {
                "test_results": self.test_results,
                "test_suites": self.test_suites,
                "coverage_reports": self.coverage_reports
            }
            
            # Generate test report
            report_prompt = f"""
            As a QA Engineer, create a comprehensive test report for: {task.title}
            
            Requirements: {task.description}
            Test Data: {test_data}
            
            Please provide:
            1. Executive summary
            2. Test execution summary
            3. Test coverage analysis
            4. Defect summary and analysis
            5. Quality metrics and trends
            6. Recommendations and next steps
            """
            
            llm_response = await self.llm_client.generate(
                prompt=report_prompt,
                system_prompt="You are a QA engineer creating comprehensive test reports."
            )
            
            if not llm_response.success:
                raise Exception(f"Test reporting failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "test_report": llm_response.content,
                    "test_data": test_data,
                    "report_timestamp": datetime.utcnow().isoformat()
                },
                logs=[f"Test report generated for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Test reporting task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Test reporting failed: {e}"]
            )
    
    async def _handle_general_test_task(self, task: Task) -> TaskResult:
        """Handle general testing tasks."""
        try:
            self.logger.info(f"Handling general test task: {task.title}")
            
            # Use LLM to process general test task
            test_prompt = f"""
            As a Senior QA Engineer, handle this testing task: {task.title}
            
            Description: {task.description}
            
            Please provide a comprehensive testing solution that addresses the task requirements,
            including testing strategies, approaches, and recommendations.
            """
            
            llm_response = await self.llm_client.generate(
                prompt=test_prompt,
                system_prompt="You are a senior QA engineer handling various testing tasks."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM processing failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "solution": llm_response.content,
                    "task_type": "general_test"
                },
                logs=[f"General test task completed: {task.title}"],
                duration_seconds=1.5
            )
            
        except Exception as e:
            self.logger.error(f"General test task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"General test task failed: {e}"]
            )
    
    async def get_capabilities(self) -> AgentCapabilities:
        """Get Test agent capabilities."""
        return self.agent.capabilities
    
    # Public methods for external interaction
    async def generate_test_suite(self, target_module: str, test_types: List[str] = None) -> Dict[str, Any]:
        """Generate a comprehensive test suite for a module."""
        try:
            test_types = test_types or ["unit", "integration"]
            generated_tests = {}
            
            for test_type in test_types:
                # Create test generation task
                test_task = create_task(
                    title=f"Generate {test_type} tests for {target_module}",
                    description=f"Create comprehensive {test_type} tests for {target_module}",
                    task_type=TaskType.TESTING,
                    created_by=self.agent.id,
                    created_by_role=self.agent.role
                )
                
                # Add context
                test_task.set_context("test_type", test_type)
                test_task.set_context("target_file", target_module)
                test_task.set_context("framework", "pytest")
                
                # Process the task
                result = await self.process_task(test_task)
                
                if result.success:
                    generated_tests[test_type] = result.output
                else:
                    generated_tests[test_type] = {"error": result.error}
            
            return {
                "success": any(test.get("test_code") for test in generated_tests.values()),
                "tests": generated_tests,
                "target_module": target_module
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate test suite: {e}")
            return {"success": False, "error": str(e)}
    
    async def run_test_suite(self, test_path: str, framework: str = "pytest") -> Dict[str, Any]:
        """Run a test suite and return results."""
        try:
            # Create test execution task
            exec_task = create_task(
                title=f"Run test suite: {test_path}",
                description=f"Execute tests in {test_path}",
                task_type=TaskType.TESTING,
                created_by=self.agent.id,
                created_by_role=self.agent.role
            )
            
            # Add context
            exec_task.set_context("test_path", test_path)
            exec_task.set_context("framework", framework)
            
            # Process the task
            result = await self.process_task(exec_task)
            
            return {
                "success": result.success,
                "results": result.output if result.success else None,
                "error": result.error
            }
            
        except Exception as e:
            self.logger.error(f"Failed to run test suite: {e}")
            return {"success": False, "error": str(e)}
    
    def get_test_results(self, test_id: Optional[str] = None) -> Dict[str, Any]:
        """Get test results."""
        if test_id:
            return self.test_results.get(test_id, {"error": "Test ID not found"})
        else:
            return {
                "total_test_runs": len(self.test_results),
                "test_results": list(self.test_results.values())
            }
    
    def get_test_suites(self) -> Dict[str, List[str]]:
        """Get all test suites."""
        return self.test_suites.copy()
