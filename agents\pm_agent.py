"""
Project Manager Agent for the Multi-Agent Development System.

This module implements the Project Manager Agent responsible for project planning,
task coordination, and team management in the multi-agent development environment.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from shared.models import (
    AgentRole, AgentCapabilities, AgentConfiguration, TaskType, TaskPriority,
    TaskStatus, Task, TaskResult, Message, MessageType, LLMProvider, ToolType,
    create_task, create_task_message, create_notification_message
)
from shared.utils import get_agent_logger, log_agent_action, get_config
from shared.llm import LLMClient
from shared.tools import ToolManager
from .base_agent import BaseAgent


class ProjectManagerAgent(BaseAgent):
    """
    Project Manager Agent responsible for:
    - Project planning and task breakdown
    - Task assignment and coordination
    - Progress monitoring and reporting
    - Team communication and management
    - Quality assurance oversight
    """
    
    def __init__(self, name: str = "PM Agent", **kwargs):
        """Initialize Project Manager Agent."""
        
        # Define PM-specific capabilities
        capabilities = AgentCapabilities(
            skills=[
                "project_planning", "task_management", "team_coordination",
                "progress_tracking", "quality_assurance", "communication",
                "risk_management", "resource_allocation"
            ],
            tools=[
                ToolType.PLANNER, ToolType.GITHUB_CLIENT, ToolType.FILE_MANAGER,
                ToolType.DOCUMENTATION_GENERATOR
            ],
            languages=["python", "javascript", "markdown"],
            frameworks=["agile", "scrum", "kanban"],
            max_concurrent_tasks=5
        )
        
        # Define PM-specific configuration
        configuration = AgentConfiguration(
            llm_provider=LLMProvider.OPENAI,
            model_name=get_config("pm_agent_model", "gpt-4-turbo"),
            temperature=get_config("pm_agent_temperature", 0.3),
            max_tokens=get_config("pm_agent_max_tokens", 2000),
            timeout_seconds=30,
            max_retries=3,
            enable_memory=True,
            enable_tools=True,
            enable_collaboration=True
        )
        
        super().__init__(
            name=name,
            role=AgentRole.PROJECT_MANAGER,
            description="Project Manager Agent responsible for planning, coordination, and team management",
            capabilities=capabilities,
            configuration=configuration,
            **kwargs
        )
        
        # PM-specific state
        self.active_projects: Dict[str, Dict[str, Any]] = {}
        self.team_members: Dict[str, Dict[str, Any]] = {}
        self.project_templates: Dict[str, Dict[str, Any]] = {}
        
        # Initialize project templates
        self._initialize_project_templates()
        
        self.logger.info("Project Manager Agent initialized")
    
    def _initialize_project_templates(self) -> None:
        """Initialize project templates for common development tasks."""
        self.project_templates = {
            "web_api": {
                "name": "Web API Development",
                "description": "Template for developing REST API applications",
                "phases": [
                    {
                        "name": "Planning & Design",
                        "tasks": [
                            "Requirements analysis",
                            "API design and documentation",
                            "Database schema design",
                            "Architecture planning"
                        ]
                    },
                    {
                        "name": "Development",
                        "tasks": [
                            "Project setup and configuration",
                            "Database models implementation",
                            "API endpoints development",
                            "Business logic implementation",
                            "Authentication and authorization"
                        ]
                    },
                    {
                        "name": "Testing & Quality",
                        "tasks": [
                            "Unit tests implementation",
                            "Integration tests",
                            "API testing",
                            "Code review and refactoring"
                        ]
                    },
                    {
                        "name": "Deployment",
                        "tasks": [
                            "Deployment configuration",
                            "Documentation finalization",
                            "Performance optimization"
                        ]
                    }
                ]
            },
            "todo_api": {
                "name": "Todo API Development",
                "description": "Template for developing a simple Todo API",
                "phases": [
                    {
                        "name": "Setup & Planning",
                        "tasks": [
                            "Project structure setup",
                            "Dependencies configuration",
                            "Database setup",
                            "API specification"
                        ]
                    },
                    {
                        "name": "Core Development",
                        "tasks": [
                            "Todo model implementation",
                            "CRUD endpoints development",
                            "Data validation",
                            "Error handling"
                        ]
                    },
                    {
                        "name": "Testing & Documentation",
                        "tasks": [
                            "Unit tests",
                            "API tests",
                            "Documentation",
                            "Code review"
                        ]
                    }
                ]
            }
        }
    
    async def process_task(self, task: Task) -> TaskResult:
        """Process PM-specific tasks."""
        try:
            self.logger.info(f"Processing PM task: {task.title}")
            
            # Determine task type and route to appropriate handler
            if "plan" in task.title.lower() or "planning" in task.description.lower():
                return await self._handle_planning_task(task)
            elif "coordinate" in task.title.lower() or "manage" in task.title.lower():
                return await self._handle_coordination_task(task)
            elif "review" in task.title.lower() or "quality" in task.title.lower():
                return await self._handle_review_task(task)
            elif "report" in task.title.lower() or "status" in task.title.lower():
                return await self._handle_reporting_task(task)
            else:
                return await self._handle_general_pm_task(task)
                
        except Exception as e:
            self.logger.error(f"Error processing PM task: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"PM task processing failed: {e}"]
            )
    
    async def _handle_planning_task(self, task: Task) -> TaskResult:
        """Handle project planning tasks."""
        try:
            self.logger.info(f"Handling planning task: {task.title}")
            
            # Extract project requirements from task
            project_type = task.get_context("project_type", "web_api")
            project_name = task.get_context("project_name", "New Project")
            requirements = task.get_context("requirements", task.description)
            
            # Generate project plan using LLM
            planning_prompt = f"""
            As a Project Manager, create a detailed project plan for: {project_name}
            
            Project Type: {project_type}
            Requirements: {requirements}
            
            Please provide:
            1. Project breakdown into phases
            2. Detailed task list for each phase
            3. Estimated timeline
            4. Resource requirements
            5. Risk assessment
            6. Success criteria
            
            Format the response as a structured plan that can be used to create tasks.
            """
            
            llm_response = await self.llm_client.generate(
                prompt=planning_prompt,
                system_prompt="You are an experienced project manager creating detailed project plans."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM planning failed: {llm_response.error}")
            
            # Create project structure
            project_id = f"project_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            project_plan = {
                "id": project_id,
                "name": project_name,
                "type": project_type,
                "description": requirements,
                "plan": llm_response.content,
                "created_at": datetime.utcnow(),
                "status": "planned",
                "tasks": []
            }
            
            # Store project
            self.active_projects[project_id] = project_plan
            
            # Create initial tasks based on template
            if project_type in self.project_templates:
                await self._create_tasks_from_template(project_id, project_type)
            
            log_agent_action(
                self.agent.id,
                self.agent.role,
                "project_planned",
                {
                    "project_id": project_id,
                    "project_name": project_name,
                    "project_type": project_type
                }
            )
            
            return TaskResult(
                success=True,
                output={
                    "project_id": project_id,
                    "project_plan": project_plan,
                    "plan_content": llm_response.content
                },
                logs=[f"Project plan created for {project_name}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Planning task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Planning failed: {e}"]
            )
    
    async def _create_tasks_from_template(self, project_id: str, project_type: str) -> List[Task]:
        """Create tasks from project template."""
        try:
            template = self.project_templates[project_type]
            created_tasks = []
            
            for phase_idx, phase in enumerate(template["phases"]):
                for task_idx, task_title in enumerate(phase["tasks"]):
                    # Create task
                    new_task = create_task(
                        title=task_title,
                        description=f"Task for {phase['name']} phase of {template['name']}",
                        task_type=TaskType.DEVELOPMENT,
                        created_by=self.agent.id,
                        created_by_role=self.agent.role,
                        priority=TaskPriority.MEDIUM
                    )
                    
                    # Add project context
                    new_task.set_context("project_id", project_id)
                    new_task.set_context("phase", phase["name"])
                    new_task.set_context("phase_index", phase_idx)
                    new_task.set_context("task_index", task_idx)
                    
                    created_tasks.append(new_task)
                    
                    # Add to project
                    self.active_projects[project_id]["tasks"].append(new_task.id)
            
            self.logger.info(f"Created {len(created_tasks)} tasks from template {project_type}")
            return created_tasks
            
        except Exception as e:
            self.logger.error(f"Failed to create tasks from template: {e}")
            return []
    
    async def _handle_coordination_task(self, task: Task) -> TaskResult:
        """Handle team coordination tasks."""
        try:
            self.logger.info(f"Handling coordination task: {task.title}")
            
            # Get team status
            team_status = await self._get_team_status()
            
            # Generate coordination plan
            coordination_prompt = f"""
            As a Project Manager, coordinate the following task: {task.title}
            
            Task Description: {task.description}
            Current Team Status: {team_status}
            
            Please provide:
            1. Task assignment recommendations
            2. Coordination strategy
            3. Communication plan
            4. Timeline and milestones
            5. Risk mitigation
            """
            
            llm_response = await self.llm_client.generate(
                prompt=coordination_prompt,
                system_prompt="You are a project manager coordinating team activities."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM coordination failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "coordination_plan": llm_response.content,
                    "team_status": team_status
                },
                logs=[f"Coordination plan created for {task.title}"],
                duration_seconds=1.5
            )
            
        except Exception as e:
            self.logger.error(f"Coordination task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Coordination failed: {e}"]
            )
    
    async def _handle_review_task(self, task: Task) -> TaskResult:
        """Handle review and quality assurance tasks."""
        try:
            self.logger.info(f"Handling review task: {task.title}")
            
            # Get review context
            review_target = task.get_context("review_target", "project")
            review_criteria = task.get_context("criteria", "quality, completeness, standards")
            
            # Generate review checklist
            review_prompt = f"""
            As a Project Manager, create a comprehensive review for: {task.title}
            
            Review Target: {review_target}
            Review Criteria: {review_criteria}
            Task Description: {task.description}
            
            Please provide:
            1. Review checklist
            2. Quality criteria
            3. Acceptance criteria
            4. Recommendations for improvement
            5. Next steps
            """
            
            llm_response = await self.llm_client.generate(
                prompt=review_prompt,
                system_prompt="You are a project manager conducting quality reviews."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM review failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "review_checklist": llm_response.content,
                    "review_target": review_target,
                    "criteria": review_criteria
                },
                logs=[f"Review completed for {task.title}"],
                duration_seconds=2.0
            )
            
        except Exception as e:
            self.logger.error(f"Review task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Review failed: {e}"]
            )
    
    async def _handle_reporting_task(self, task: Task) -> TaskResult:
        """Handle reporting and status update tasks."""
        try:
            self.logger.info(f"Handling reporting task: {task.title}")
            
            # Gather project data
            project_data = await self._gather_project_data()
            
            # Generate status report
            report_prompt = f"""
            As a Project Manager, create a status report for: {task.title}
            
            Task Description: {task.description}
            Project Data: {project_data}
            
            Please provide:
            1. Executive summary
            2. Progress overview
            3. Key achievements
            4. Current challenges
            5. Next steps and timeline
            6. Resource status
            7. Risk assessment
            """
            
            llm_response = await self.llm_client.generate(
                prompt=report_prompt,
                system_prompt="You are a project manager creating comprehensive status reports."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM reporting failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "status_report": llm_response.content,
                    "project_data": project_data,
                    "report_timestamp": datetime.utcnow().isoformat()
                },
                logs=[f"Status report generated for {task.title}"],
                duration_seconds=1.8
            )
            
        except Exception as e:
            self.logger.error(f"Reporting task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"Reporting failed: {e}"]
            )
    
    async def _handle_general_pm_task(self, task: Task) -> TaskResult:
        """Handle general PM tasks."""
        try:
            self.logger.info(f"Handling general PM task: {task.title}")
            
            # Use LLM to process general PM task
            pm_prompt = f"""
            As a Project Manager, handle this task: {task.title}
            
            Description: {task.description}
            
            Please provide a comprehensive response that addresses the task requirements
            from a project management perspective, including any recommendations,
            action items, or deliverables.
            """
            
            llm_response = await self.llm_client.generate(
                prompt=pm_prompt,
                system_prompt="You are an experienced project manager handling various project management tasks."
            )
            
            if not llm_response.success:
                raise Exception(f"LLM processing failed: {llm_response.error}")
            
            return TaskResult(
                success=True,
                output={
                    "response": llm_response.content,
                    "task_type": "general_pm"
                },
                logs=[f"General PM task completed: {task.title}"],
                duration_seconds=1.5
            )
            
        except Exception as e:
            self.logger.error(f"General PM task failed: {e}")
            return TaskResult(
                success=False,
                output=None,
                error=str(e),
                logs=[f"General PM task failed: {e}"]
            )
    
    async def _get_team_status(self) -> Dict[str, Any]:
        """Get current team status."""
        try:
            # Get registered agents from communication hub
            if self.communication_hub:
                agents = self.communication_hub.get_registered_agents()
                
                team_status = {
                    "total_agents": len(agents),
                    "agents_by_role": {},
                    "active_agents": 0,
                    "agents": []
                }
                
                for agent_id, agent_info in agents.items():
                    role = agent_info.get("role", "unknown")
                    status = agent_info.get("status", "unknown")
                    
                    if role not in team_status["agents_by_role"]:
                        team_status["agents_by_role"][role] = 0
                    team_status["agents_by_role"][role] += 1
                    
                    if status in ["idle", "working"]:
                        team_status["active_agents"] += 1
                    
                    team_status["agents"].append({
                        "id": agent_id,
                        "name": agent_info.get("name", "Unknown"),
                        "role": role,
                        "status": status
                    })
                
                return team_status
            
            return {"total_agents": 0, "message": "No communication hub available"}
            
        except Exception as e:
            self.logger.error(f"Failed to get team status: {e}")
            return {"error": str(e)}
    
    async def _gather_project_data(self) -> Dict[str, Any]:
        """Gather comprehensive project data."""
        try:
            project_data = {
                "active_projects": len(self.active_projects),
                "projects": list(self.active_projects.values()),
                "team_status": await self._get_team_status(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            return project_data
            
        except Exception as e:
            self.logger.error(f"Failed to gather project data: {e}")
            return {"error": str(e)}
    
    async def get_capabilities(self) -> AgentCapabilities:
        """Get PM agent capabilities."""
        return self.agent.capabilities
    
    # Public methods for external interaction
    async def create_project_plan(self, project_name: str, project_type: str, requirements: str) -> Dict[str, Any]:
        """Create a new project plan."""
        try:
            # Create planning task
            planning_task = create_task(
                title=f"Create project plan for {project_name}",
                description=f"Plan {project_type} project: {requirements}",
                task_type=TaskType.PLANNING,
                created_by=self.agent.id,
                created_by_role=self.agent.role
            )
            
            # Add context
            planning_task.set_context("project_name", project_name)
            planning_task.set_context("project_type", project_type)
            planning_task.set_context("requirements", requirements)
            
            # Process the task
            result = await self.process_task(planning_task)
            
            return {
                "success": result.success,
                "project_plan": result.output if result.success else None,
                "error": result.error
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create project plan: {e}")
            return {"success": False, "error": str(e)}
    
    async def assign_task_to_agent(self, task: Task, agent_role: AgentRole) -> bool:
        """Assign a task to an agent with specific role."""
        try:
            # Create task assignment message
            assignment_message = create_task_message(
                sender_id=self.agent.id,
                sender_role=self.agent.role,
                task_id=task.id,
                action="assign",
                receiver_role=agent_role,
                parameters={"task_data": task.to_dict()}
            )
            
            # Send through communication hub
            if self.communication_hub:
                success = await self.communication_hub.send_message(assignment_message)
                
                if success:
                    log_agent_action(
                        self.agent.id,
                        self.agent.role,
                        "task_assigned",
                        {
                            "task_id": task.id,
                            "assigned_to_role": agent_role.value
                        }
                    )
                
                return success
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to assign task: {e}")
            return False
    
    def get_project_status(self, project_id: Optional[str] = None) -> Dict[str, Any]:
        """Get status of specific project or all projects."""
        try:
            if project_id:
                if project_id in self.active_projects:
                    return self.active_projects[project_id]
                else:
                    return {"error": f"Project {project_id} not found"}
            else:
                return {
                    "total_projects": len(self.active_projects),
                    "projects": list(self.active_projects.values())
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get project status: {e}")
            return {"error": str(e)}
