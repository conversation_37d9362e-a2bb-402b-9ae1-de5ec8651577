# Multi-Agent Development System - Project Structure

```
multi-agent-dev-system/
├── README.md
├── architecture.md
├── requirements.txt
├── pyproject.toml
├── .env.example
├── .gitignore
├── docker-compose.yml
├── Dockerfile
│
├── agents/                     # Agent implementations
│   ├── __init__.py
│   ├── base_agent.py          # Base agent class
│   ├── pm_agent/              # Project Manager Agent
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── agent.py
│   │   ├── tools.py
│   │   └── Dockerfile
│   ├── dev_agent/             # Developer Agent
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── agent.py
│   │   ├── tools.py
│   │   └── Dockerfile
│   └── test_agent/            # Test Agent
│       ├── __init__.py
│       ├── main.py
│       ├── agent.py
│       ├── tools.py
│       └── Dockerfile
│
├── shared/                     # Shared components
│   ├── __init__.py
│   ├── models/                # Data models
│   │   ├── __init__.py
│   │   ├── agent.py
│   │   ├── message.py
│   │   ├── task.py
│   │   └── enums.py
│   ├── communication/         # Communication layer
│   │   ├── __init__.py
│   │   ├── hub.py
│   │   ├── redis_client.py
│   │   └── websocket_server.py
│   ├── llm/                   # LLM integration
│   │   ├── __init__.py
│   │   ├── client.py
│   │   ├── providers/
│   │   │   ├── __init__.py
│   │   │   ├── openai_provider.py
│   │   │   ├── anthropic_provider.py
│   │   │   └── ollama_provider.py
│   │   └── config.py
│   ├── tools/                 # Shared tools
│   │   ├── __init__.py
│   │   ├── github_client.py
│   │   ├── code_executor.py
│   │   └── file_manager.py
│   └── utils/                 # Utilities
│       ├── __init__.py
│       ├── logger.py
│       ├── config.py
│       └── helpers.py
│
├── dashboard/                  # Web dashboard
│   ├── __init__.py
│   ├── app.py                 # FastAPI application
│   ├── static/                # Static files
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   ├── templates/             # HTML templates
│   │   ├── index.html
│   │   ├── chat.html
│   │   └── agents.html
│   └── api/                   # API endpoints
│       ├── __init__.py
│       ├── agents.py
│       ├── messages.py
│       └── tasks.py
│
├── config/                     # Configuration files
│   ├── agents.yaml
│   ├── llm_config.yaml
│   ├── redis_config.yaml
│   └── logging_config.yaml
│
├── tests/                      # Test files
│   ├── __init__.py
│   ├── conftest.py
│   ├── unit/
│   │   ├── test_agents/
│   │   ├── test_models/
│   │   └── test_communication/
│   ├── integration/
│   │   ├── test_agent_communication.py
│   │   └── test_workflow.py
│   └── e2e/
│       └── test_todo_project.py
│
├── scripts/                    # Utility scripts
│   ├── start_agents.py
│   ├── setup_environment.py
│   └── run_tests.py
│
├── docs/                       # Documentation
│   ├── api/
│   ├── agents/
│   └── deployment/
│
├── examples/                   # Example projects
│   └── todo_api/
│       ├── requirements.txt
│       ├── main.py
│       ├── models.py
│       └── tests/
│
└── monitoring/                 # Monitoring and metrics
    ├── __init__.py
    ├── metrics.py
    ├── health_checks.py
    └── prometheus_config.yml
```

## Key Directories Explanation

### `/agents/`
- Contains all agent implementations
- Each agent has its own directory with main.py, agent.py, tools.py
- Dockerfile for containerization

### `/shared/`
- Common components used by all agents
- Models, communication layer, LLM integration
- Utilities and helper functions

### `/dashboard/`
- Web-based monitoring and control interface
- Real-time chat view
- Agent status monitoring

### `/config/`
- YAML configuration files
- Environment-specific settings
- Agent behavior configuration

### `/tests/`
- Comprehensive test suite
- Unit, integration, and end-to-end tests
- Test fixtures and utilities

This structure provides:
- Clear separation of concerns
- Easy scalability for new agents
- Shared components for reusability
- Comprehensive testing framework
- Production-ready deployment setup
