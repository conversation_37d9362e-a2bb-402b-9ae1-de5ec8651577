[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "multi-agent-dev-system"
version = "1.0.0"
description = "Autonomous software development system with multiple AI agents"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
maintainers = [
    {name = "inkbytefo", email = "<EMAIL>"}
]
keywords = [
    "ai", "agents", "multi-agent", "autonomous", "development", 
    "llm", "automation", "software-engineering"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.11"
dependencies = [
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    "fastapi>=0.108.0",
    "uvicorn[standard]>=0.25.0",
    "aioredis>=2.0.1",
    "openai>=1.6.0",
    "anthropic>=0.8.0",
    "langchain>=0.1.0",
    "langgraph>=0.0.20",
    "chromadb>=0.4.20",
    "PyGithub>=2.1.0",
    "pytest>=7.4.0",
    "structlog>=23.2.0",
    "click>=8.1.0",
    "rich>=13.7.0",
    "httpx>=0.26.0",
    "pyyaml>=6.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.12.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.23.0",
    "pytest-mock>=3.12.0",
    "bandit>=1.7.5",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "coverage>=7.3.0",
    "factory-boy>=3.3.0",
    "responses>=0.24.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.24.0",
]
monitoring = [
    "prometheus-client>=0.19.0",
    "grafana-api>=1.0.3",
]
vector-db = [
    "pinecone-client>=2.2.4",
    "weaviate-client>=3.25.0",
]
local-llm = [
    "ollama>=0.1.7",
]
all = [
    "multi-agent-dev-system[dev,test,docs,monitoring,vector-db,local-llm]"
]

[project.urls]
Homepage = "https://github.com/inkbytefo/multi-agent-dev-system"
Documentation = "https://docs.multi-agent-dev.com"
Repository = "https://github.com/inkbytefo/multi-agent-dev-system.git"
Issues = "https://github.com/inkbytefo/multi-agent-dev-system/issues"
Changelog = "https://github.com/inkbytefo/multi-agent-dev-system/blob/main/CHANGELOG.md"

[project.scripts]
multi-agent = "scripts.start_agents:main"
setup-env = "scripts.setup_environment:main"
run-tests = "scripts.run_tests:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["agents*", "shared*", "dashboard*", "scripts*", "monitoring*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
"*" = ["*.yaml", "*.yml", "*.json", "*.toml", "*.txt", "*.md"]

# =============================================================================
# Development Tools Configuration
# =============================================================================

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["agents", "shared", "dashboard", "monitoring"]
known_third_party = ["fastapi", "pydantic", "openai", "anthropic", "langchain"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "redis.*",
    "chromadb.*",
    "github.*",
    "langchain.*",
    "langgraph.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=agents",
    "--cov=shared",
    "--cov=dashboard",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
    "unit: marks tests as unit tests",
    "agent: marks tests related to agent functionality",
    "communication: marks tests related to communication layer",
    "llm: marks tests related to LLM integration",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["agents", "shared", "dashboard", "monitoring"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/conftest.py",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "docs", "examples"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "docs/source/conf.py",
    "old",
    "build",
    "dist",
    ".venv",
    "venv",
    ".eggs",
    "*.egg",
]
per-file-ignores = [
    "__init__.py:F401",
    "tests/*:S101,S106",
]

# =============================================================================
# Environment-specific configurations
# =============================================================================

[tool.setuptools.dynamic]
version = {attr = "shared.__version__"}

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["S101"]
