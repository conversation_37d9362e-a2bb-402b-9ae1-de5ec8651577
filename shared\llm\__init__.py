"""
LLM integration module for the Multi-Agent Development System.

This module provides unified access to different LLM providers including
OpenAI, Anthropic, and Ollama with automatic fallback and error handling.
"""

from .client import (
    LLMClient,
    LLMRequest,
    LLMResponse,
    BaseLLMProvider,
    OpenAIProvider,
    AnthropicProvider,
    OllamaProvider,
)

# Version information
__version__ = "1.0.0"
__author__ = "inkbytefo"

# Export all public classes
__all__ = [
    "LLMClient",
    "LLMRequest",
    "LLMResponse",
    "BaseLLMProvider",
    "OpenAIProvider",
    "AnthropicProvider",
    "OllamaProvider",
]