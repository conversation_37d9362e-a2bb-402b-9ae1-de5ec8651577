"""
WebSocket Server for the Multi-Agent Development System.

This module provides real-time communication capabilities through WebSocket
connections for monitoring agents and receiving live updates.
"""

import asyncio
import json
import uuid
from typing import Dict, Set, Optional, Any, List
from datetime import datetime

import socketio
from aiohttp import web, WSMsgType
from aiohttp.web_ws import WebSocketResponse

from ..models import Message, MessageType, AgentRole
from ..utils import (
    get_logger, handle_error, CommunicationException, ErrorCode,
    get_config, log_communication_event
)
from .hub import CommunicationHub


class WebSocketServer:
    """WebSocket server for real-time communication."""
    
    def __init__(self, communication_hub: CommunicationHub, host: str = "localhost", port: int = 8765):
        """Initialize WebSocket server."""
        self.logger = get_logger("websocket_server")
        
        self.host = host or get_config("websocket_host", "localhost")
        self.port = port or get_config("websocket_port", 8765)
        self.communication_hub = communication_hub
        
        # Socket.IO server
        self.sio = socketio.AsyncServer(
            cors_allowed_origins="*",
            logger=False,
            engineio_logger=False
        )
        
        # Web application
        self.app = web.Application()
        self.sio.attach(self.app)
        
        # Connected clients
        self.clients: Dict[str, Dict[str, Any]] = {}  # session_id -> client_info
        self.agent_clients: Dict[str, str] = {}  # agent_id -> session_id
        
        # Setup event handlers
        self._setup_event_handlers()
        
        # Server state
        self.running = False
        self.server = None
        
        self.logger.info(f"WebSocket server initialized on {self.host}:{self.port}")
    
    def _setup_event_handlers(self) -> None:
        """Setup Socket.IO event handlers."""
        
        @self.sio.event
        async def connect(sid, environ):
            """Handle client connection."""
            try:
                client_info = {
                    "session_id": sid,
                    "connected_at": datetime.utcnow(),
                    "client_type": "dashboard",  # Default type
                    "agent_id": None,
                    "last_activity": datetime.utcnow()
                }
                
                self.clients[sid] = client_info
                
                self.logger.info(f"Client connected: {sid}")
                
                # Send initial data
                await self._send_initial_data(sid)
                
                log_communication_event(
                    "websocket_client_connected",
                    details={"session_id": sid}
                )
                
            except Exception as e:
                self.logger.error(f"Error handling connection: {e}")
        
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection."""
            try:
                if sid in self.clients:
                    client_info = self.clients[sid]
                    agent_id = client_info.get("agent_id")
                    
                    # Remove from agent clients if it was an agent
                    if agent_id and agent_id in self.agent_clients:
                        del self.agent_clients[agent_id]
                    
                    del self.clients[sid]
                    
                    self.logger.info(f"Client disconnected: {sid}")
                    
                    log_communication_event(
                        "websocket_client_disconnected",
                        details={"session_id": sid, "agent_id": agent_id}
                    )
                
            except Exception as e:
                self.logger.error(f"Error handling disconnection: {e}")
        
        @self.sio.event
        async def register_agent(sid, data):
            """Register an agent client."""
            try:
                agent_id = data.get("agent_id")
                agent_name = data.get("agent_name")
                agent_role = data.get("agent_role")
                
                if not agent_id:
                    await self.sio.emit("error", {"message": "agent_id required"}, room=sid)
                    return
                
                # Update client info
                if sid in self.clients:
                    self.clients[sid].update({
                        "client_type": "agent",
                        "agent_id": agent_id,
                        "agent_name": agent_name,
                        "agent_role": agent_role
                    })
                    
                    self.agent_clients[agent_id] = sid
                    
                    await self.sio.emit("registered", {"status": "success"}, room=sid)
                    
                    # Broadcast agent registration to dashboard clients
                    await self._broadcast_to_dashboards("agent_registered", {
                        "agent_id": agent_id,
                        "agent_name": agent_name,
                        "agent_role": agent_role
                    })
                    
                    self.logger.info(f"Agent registered: {agent_name} ({agent_id})")
                
            except Exception as e:
                self.logger.error(f"Error registering agent: {e}")
                await self.sio.emit("error", {"message": str(e)}, room=sid)
        
        @self.sio.event
        async def send_message(sid, data):
            """Handle message sending from clients."""
            try:
                # Validate client
                if sid not in self.clients:
                    return
                
                client_info = self.clients[sid]
                
                # Create message
                message = Message(
                    sender_id=client_info.get("agent_id", "websocket_client"),
                    sender_role=AgentRole.COORDINATOR,
                    receiver_id=data.get("receiver_id"),
                    message_type=MessageType(data.get("message_type", "notification")),
                    content=data.get("content", "")
                )
                
                # Send through communication hub
                success = await self.communication_hub.send_message(message)
                
                await self.sio.emit("message_sent", {"success": success}, room=sid)
                
            except Exception as e:
                self.logger.error(f"Error sending message: {e}")
                await self.sio.emit("error", {"message": str(e)}, room=sid)
        
        @self.sio.event
        async def get_agents(sid, data):
            """Get list of registered agents."""
            try:
                agents = self.communication_hub.get_registered_agents()
                await self.sio.emit("agents_list", {"agents": agents}, room=sid)
                
            except Exception as e:
                self.logger.error(f"Error getting agents: {e}")
                await self.sio.emit("error", {"message": str(e)}, room=sid)
        
        @self.sio.event
        async def get_statistics(sid, data):
            """Get system statistics."""
            try:
                stats = self.communication_hub.get_statistics()
                await self.sio.emit("statistics", {"stats": stats}, room=sid)
                
            except Exception as e:
                self.logger.error(f"Error getting statistics: {e}")
                await self.sio.emit("error", {"message": str(e)}, room=sid)
    
    async def start(self) -> None:
        """Start the WebSocket server."""
        try:
            self.logger.info(f"Starting WebSocket server on {self.host}:{self.port}")
            
            # Setup routes
            self.app.router.add_get("/", self._handle_index)
            self.app.router.add_get("/health", self._handle_health)
            
            # Start server
            runner = web.AppRunner(self.app)
            await runner.setup()
            
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()
            
            self.running = True
            self.server = runner
            
            # Start background tasks
            asyncio.create_task(self._message_broadcaster())
            asyncio.create_task(self._status_broadcaster())
            
            self.logger.info(f"WebSocket server started on {self.host}:{self.port}")
            
        except Exception as e:
            self.logger.error(f"Failed to start WebSocket server: {e}")
            raise CommunicationException(
                f"WebSocket server startup failed: {str(e)}",
                error_code=ErrorCode.COMMUNICATION_CHANNEL_ERROR
            )
    
    async def stop(self) -> None:
        """Stop the WebSocket server."""
        try:
            self.logger.info("Stopping WebSocket server")
            
            self.running = False
            
            # Disconnect all clients
            for sid in list(self.clients.keys()):
                await self.sio.disconnect(sid)
            
            # Stop server
            if self.server:
                await self.server.cleanup()
                self.server = None
            
            self.clients.clear()
            self.agent_clients.clear()
            
            self.logger.info("WebSocket server stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping WebSocket server: {e}")
    
    async def _send_initial_data(self, sid: str) -> None:
        """Send initial data to newly connected client."""
        try:
            # Send registered agents
            agents = self.communication_hub.get_registered_agents()
            await self.sio.emit("agents_list", {"agents": agents}, room=sid)
            
            # Send statistics
            stats = self.communication_hub.get_statistics()
            await self.sio.emit("statistics", {"stats": stats}, room=sid)
            
            # Send recent message history
            history = self.communication_hub.get_message_history(limit=50)
            message_data = [msg.to_dict() for msg in history]
            await self.sio.emit("message_history", {"messages": message_data}, room=sid)
            
        except Exception as e:
            self.logger.error(f"Error sending initial data: {e}")
    
    async def _broadcast_to_dashboards(self, event: str, data: Any) -> None:
        """Broadcast event to dashboard clients."""
        try:
            dashboard_clients = [
                sid for sid, client_info in self.clients.items()
                if client_info.get("client_type") == "dashboard"
            ]
            
            for sid in dashboard_clients:
                await self.sio.emit(event, data, room=sid)
                
        except Exception as e:
            self.logger.error(f"Error broadcasting to dashboards: {e}")
    
    async def _broadcast_to_agents(self, event: str, data: Any) -> None:
        """Broadcast event to agent clients."""
        try:
            agent_clients = [
                sid for sid, client_info in self.clients.items()
                if client_info.get("client_type") == "agent"
            ]
            
            for sid in agent_clients:
                await self.sio.emit(event, data, room=sid)
                
        except Exception as e:
            self.logger.error(f"Error broadcasting to agents: {e}")
    
    async def _message_broadcaster(self) -> None:
        """Background task to broadcast new messages."""
        last_message_count = 0
        
        while self.running:
            try:
                # Get recent messages
                history = self.communication_hub.get_message_history()
                current_count = len(history)
                
                if current_count > last_message_count:
                    # New messages available
                    new_messages = history[last_message_count:]
                    
                    for message in new_messages:
                        await self._broadcast_to_dashboards("new_message", message.to_dict())
                    
                    last_message_count = current_count
                
                await asyncio.sleep(1)  # Check every second
                
            except Exception as e:
                self.logger.error(f"Error in message broadcaster: {e}")
                await asyncio.sleep(5)
    
    async def _status_broadcaster(self) -> None:
        """Background task to broadcast status updates."""
        while self.running:
            try:
                # Broadcast statistics
                stats = self.communication_hub.get_statistics()
                await self._broadcast_to_dashboards("statistics_update", {"stats": stats})
                
                # Broadcast agent statuses
                agents = self.communication_hub.get_registered_agents()
                await self._broadcast_to_dashboards("agents_update", {"agents": agents})
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in status broadcaster: {e}")
                await asyncio.sleep(30)
    
    async def _handle_index(self, request) -> web.Response:
        """Handle index page request."""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Multi-Agent System Dashboard</title>
            <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
        </head>
        <body>
            <h1>Multi-Agent Development System</h1>
            <p>WebSocket server is running. Connect with Socket.IO client for real-time updates.</p>
            <div id="status">Connecting...</div>
            
            <script>
                const socket = io();
                
                socket.on('connect', function() {
                    document.getElementById('status').textContent = 'Connected to WebSocket server';
                });
                
                socket.on('disconnect', function() {
                    document.getElementById('status').textContent = 'Disconnected from WebSocket server';
                });
                
                socket.on('new_message', function(data) {
                    console.log('New message:', data);
                });
                
                socket.on('statistics_update', function(data) {
                    console.log('Statistics update:', data);
                });
            </script>
        </body>
        </html>
        """
        return web.Response(text=html, content_type="text/html")
    
    async def _handle_health(self, request) -> web.Response:
        """Handle health check request."""
        health_data = {
            "status": "healthy" if self.running else "stopped",
            "connected_clients": len(self.clients),
            "agent_clients": len(self.agent_clients),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return web.json_response(health_data)
    
    # Public methods for external use
    async def broadcast_message(self, message: Message) -> None:
        """Broadcast message to all connected clients."""
        await self._broadcast_to_dashboards("new_message", message.to_dict())
    
    async def notify_agent_status_change(self, agent_id: str, status: str) -> None:
        """Notify clients of agent status change."""
        await self._broadcast_to_dashboards("agent_status_change", {
            "agent_id": agent_id,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def get_connected_clients(self) -> Dict[str, Dict[str, Any]]:
        """Get information about connected clients."""
        return self.clients.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get WebSocket server statistics."""
        return {
            "running": self.running,
            "host": self.host,
            "port": self.port,
            "connected_clients": len(self.clients),
            "agent_clients": len(self.agent_clients),
            "dashboard_clients": len([
                c for c in self.clients.values()
                if c.get("client_type") == "dashboard"
            ])
        }
